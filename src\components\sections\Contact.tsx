import React from 'react';
import { useNavigate } from 'react-router-dom';
import Button from '../ui/Button';

// Import flag icons
import { IN } from 'country-flag-icons/react/3x2';
import { JP } from 'country-flag-icons/react/3x2';
import { US } from 'country-flag-icons/react/3x2';

const Contact: React.FC = () => {
  const navigate = useNavigate();

  const handleScheduleConsultation = () => {
    // Navigate to booking page
    navigate('/booking');
  };

  return (
    <section className="py-16 sm:py-20 md:py-24 bg-gradient-to-br from-orange-50 via-white to-amber-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-orange-600 to-amber-700 text-white rounded-full text-sm font-medium mb-8 shadow-lg">
          <span className="w-3 h-3 bg-white rounded-full mr-3 animate-pulse"></span>
            Global Presence
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-heading font-extrabold text-gray-900 mb-6 leading-tight tracking-tight">
            Our <span className="bg-gradient-to-r from-orange-600 to-amber-700 bg-clip-text text-transparent">Global</span> Presence
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto font-medium">
             With offices across three continents, we're always close to our clients and ready to serve you locally.
          </p>
        </div>



        {/* Global Locations Section */}
        <div className="mt-20 mb-16">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* India Office */}
            <div className="group relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-orange-200 hover:border-orange-400 hover:shadow-xl transition-all duration-300 text-center">
              <div className="absolute inset-0 bg-gradient-to-br from-orange-50 to-amber-50 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative z-10">
                <div className="w-14 h-10 mx-auto mb-6 border-2 border-gray-200">
                  <IN className="w-full h-full object-cover" />
                </div>
                <h4 className="text-xl font-heading font-bold text-gray-900 mb-2">India Office</h4>
                <p className="text-orange-700 font-semibold mb-2">Chennai, Tamil Nadu</p>
                <p className="text-gray-600 text-sm leading-relaxed">
                  Our headquarters and main development center, serving clients across Asia-Pacific region.
                </p>
                <div className="mt-4 pt-4 border-t border-orange-200">
                  <p className="text-xs text-gray-500">Primary Hub</p>
                </div>
              </div>
            </div>

            {/* Japan Office */}
            <div className="group relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-orange-200 hover:border-orange-400 hover:shadow-xl transition-all duration-300 text-center">
              <div className="absolute inset-0 bg-gradient-to-br from-orange-50 to-amber-50 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative z-10">
                <div className="w-14 h-10 mx-auto mb-6 border-2 border-gray-200">
                  <JP className="w-full h-full object-cover" />
                </div>
                <h4 className="text-xl font-heading font-bold text-gray-900 mb-2">Japan Office</h4>
                <p className="text-orange-700 font-semibold mb-2">Kariya, Aichi</p>
                <p className="text-gray-600 text-sm leading-relaxed">
                  Strategic location serving Japanese market with cutting-edge technology solutions.
                </p>
                <div className="mt-4 pt-4 border-t border-orange-200">
                  <p className="text-xs text-gray-500">Asia Operations</p>
                </div>
              </div>
            </div>

            {/* USA Office */}
            <div className="group relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-orange-200 hover:border-orange-400 hover:shadow-xl transition-all duration-300 text-center">
              <div className="absolute inset-0 bg-gradient-to-br from-orange-50 to-amber-50 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative z-10">
                <div className="w-14 h-10 mx-auto mb-6 border-2 border-gray-200">
                  <US className="w-full h-full object-cover" />
                </div>
                <h4 className="text-xl font-heading font-bold text-gray-900 mb-2">USA Office</h4>
                <p className="text-orange-700 font-semibold mb-2">Sammamish, Washington</p>
                <p className="text-gray-600 text-sm leading-relaxed">
                  North American headquarters providing enterprise solutions and client support.
                </p>
                <div className="mt-4 pt-4 border-t border-orange-200">
                  <p className="text-xs text-gray-500">Americas Hub</p>
                </div>
              </div>
            </div>
          </div>

          {/* Global Stats - Full Width */}
          <div className="mt-12">
            <div className="w-full bg-gradient-to-r from-orange-100 via-amber-50 to-orange-100 rounded-3xl border border-orange-200 p-8 shadow-lg">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 text-center">
                <div className="group">
                  <div className="text-3xl lg:text-4xl font-bold text-orange-700 mb-2 group-hover:scale-110 transition-transform duration-300">3</div>
                  <div className="text-sm font-medium text-gray-700">Countries</div>
                  <div className="text-xs text-gray-500 mt-1">Global Reach</div>
                </div>

                <div className="group">
                  <div className="text-3xl lg:text-4xl font-bold text-orange-700 mb-2 group-hover:scale-110 transition-transform duration-300">30+</div>
                  <div className="text-sm font-medium text-gray-700">Employees</div>
                  <div className="text-xs text-gray-500 mt-1">Expert Team</div>
                </div>

                <div className="group">
                  <div className="text-3xl lg:text-4xl font-bold text-orange-700 mb-2 group-hover:scale-110 transition-transform duration-300">24/7</div>
                  <div className="text-sm font-medium text-gray-700">Support</div>
                  <div className="text-xs text-gray-500 mt-1">Always Available</div>
                </div>

                <div className="group">
                  <div className="text-3xl lg:text-4xl font-bold text-orange-700 mb-2 group-hover:scale-110 transition-transform duration-300">15+</div>
                  <div className="text-sm font-medium text-gray-700">Time Zones</div>
                  <div className="text-xs text-gray-500 mt-1">Coverage</div>
                </div>

                <div className="group">
                  <div className="text-3xl lg:text-4xl font-bold text-orange-700 mb-2 group-hover:scale-110 transition-transform duration-300">500+</div>
                  <div className="text-sm font-medium text-gray-700">Projects</div>
                  <div className="text-xs text-gray-500 mt-1">Delivered</div>
                </div>

                <div className="group">
                  <div className="text-3xl lg:text-4xl font-bold text-orange-700 mb-2 group-hover:scale-110 transition-transform duration-300">25+</div>
                  <div className="text-sm font-medium text-gray-700">Years</div>
                  <div className="text-xs text-gray-500 mt-1">Experience</div>
                </div>
              </div>

            </div>
          </div>
        </div>

        {/* Bottom CTA - Full Width */}
        <div className="mt-16">
          <div className="w-full flex flex-col lg:flex-row items-center justify-between space-y-6 lg:space-y-0 lg:space-x-8 p-8 bg-gradient-to-r from-orange-600 to-amber-700 rounded-3xl text-white shadow-xl">
            <div className="text-center lg:text-left flex-1">
              <h4 className="text-2xl font-heading font-bold mb-2">Ready to Start Your Project?</h4>
              <p className="text-orange-100">Let's discuss how we can help you achieve your goals.</p>
            </div>
            <div className="w-full lg:w-auto lg:flex-shrink-0">
              <Button
                variant="secondary"
                size="lg"
                onClick={handleScheduleConsultation}
                className="w-full lg:w-auto bg-white text-orange-700 hover:bg-orange-50 border-0 shadow-lg px-8 py-4 text-lg font-semibold"
              >
                Schedule Consultation
              </Button>
            </div>
          </div>
        </div>

      </div>
    </section>
  );
};

export default Contact;
