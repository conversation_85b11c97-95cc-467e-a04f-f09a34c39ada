import React, { useState } from 'react';
import {
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  PaperAirplaneIcon,
  UserIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline';
import Button from '../components/ui/Button';

const Contact: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    phone: '',
    message: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Form submitted:', formData);
    // You can add your form submission logic here
  };

  const contactMethods = [
    {
      icon: <EnvelopeIcon className="w-6 h-6" />,
      title: 'Email Us',
      value: '<EMAIL>',
      description: 'Send us an email anytime'
    },
    {
      icon: <PhoneIcon className="w-6 h-6" />,
      title: 'Call Us',
      value: '+91 ************',
      description: 'Mon-Fri from 9am to 6pm'
    },
    {
      icon: <MapPinIcon className="w-6 h-6" />,
      title: 'Visit Us',
      value: 'GF1, C Block, Aarthi Towers, East Tambaram, Chennai, Tamil Nadu, India',
      description: 'Come say hello at our office'
    }
  ];

  return (
    <div className="pt-20 min-h-screen bg-gradient-to-br from-orange-50 via-white to-amber-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-heading font-bold text-gray-900 mb-6 tracking-tight">
            Get In <span className="bg-gradient-to-r from-orange-600 to-amber-700 bg-clip-text text-transparent">Touch</span>
          </h1>
          <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto font-medium">
            Ready to transform your ideas into reality? Let's discuss your project and see how our 25+ years of expertise can help you succeed.
          </p>
        </div>

        {/* Contact Methods - Mobile Responsive (Rows on Mobile) */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-16">
          {contactMethods.map((method, index) => (
            <div
              key={index}
              className="flex items-start space-x-4 p-6 rounded-2xl bg-white/60 backdrop-blur-sm border border-orange-200 hover:border-orange-400 hover:shadow-lg transition-all duration-300"
            >
              <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-orange-500 to-amber-600 rounded-xl flex items-center justify-center text-white shadow-md">
                {method.icon}
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-lg font-semibold text-gray-900 mb-1">{method.title}</h4>
                <p className="text-orange-700 font-medium mb-1 break-words">{method.value}</p>
                <p className="text-sm text-gray-600">{method.description}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Main Content Grid - Form and Map */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-start">
          {/* Left side - Contact Form */}
          <div className="order-1 lg:order-1">
            <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-orange-200 shadow-xl">
              <h3 className="text-2xl font-heading font-bold text-gray-900 mb-6 tracking-tight">
                Send us a Message
              </h3>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-semibold text-gray-700 mb-2">
                    Full Name *
                  </label>
                  <div className="relative">
                    <UserIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="text"
                      id="name"
                      name="name"
                      required
                      value={formData.name}
                      onChange={handleInputChange}
                      className="w-full pl-10 pr-4 py-3 border border-orange-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200"
                      placeholder="Your full name"
                    />
                  </div>
                </div>
                <div>
                  <label htmlFor="email" className="block text-sm font-semibold text-gray-700 mb-2">
                    Email Address *
                  </label>
                  <div className="relative">
                    <EnvelopeIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="email"
                      id="email"
                      name="email"
                      required
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full pl-10 pr-4 py-3 border border-orange-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="company" className="block text-sm font-semibold text-gray-700 mb-2">
                    Company
                  </label>
                  <div className="relative">
                    <BuildingOfficeIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="text"
                      id="company"
                      name="company"
                      value={formData.company}
                      onChange={handleInputChange}
                      className="w-full pl-10 pr-4 py-3 border border-orange-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200"
                      placeholder="Your company"
                    />
                  </div>
                </div>
                <div>
                  <label htmlFor="phone" className="block text-sm font-semibold text-gray-700 mb-2">
                    Phone Number
                  </label>
                  <div className="relative">
                    <PhoneIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full pl-10 pr-4 py-3 border border-orange-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200"
                      placeholder="+91 (*************"
                    />
                  </div>
                </div>
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-semibold text-gray-700 mb-2">
                  Project Details *
                </label>
                <textarea
                  id="message"
                  name="message"
                  required
                  rows={5}
                  value={formData.message}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-orange-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200 resize-none"
                  placeholder="Tell us about your project, requirements, timeline, and any specific needs..."
                />
              </div>

              <Button
                type="submit"
                variant="primary"
                size="lg"
                className="w-full flex items-center justify-center space-x-2"
              >
                <PaperAirplaneIcon className="w-5 h-5" />
                <span>Send Message</span>
              </Button>
            </form>
            </div>
          </div>

          {/* Right side - Google Maps */}
          <div className="order-2 lg:order-2">
            <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-6 border border-orange-200 shadow-xl">
              <h3 className="text-2xl font-heading font-bold text-gray-900 mb-6 tracking-tight">
                Find Us Here
              </h3>
              <div className="relative overflow-hidden rounded-2xl">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d243.04822007123255!2d80.12796909838654!3d12.922401218567032!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3a525f115336db2d%3A0xddf5dad187e5558e!2sMothercode%20Software%20Systems!5e0!3m2!1sen!2sin!4v1753241366046!5m2!1sen!2sin"
                  width="100%"
                  height="400"
                  style={{ border: 0 }}
                  allowFullScreen
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  className="w-full h-96 lg:h-[400px]"
                />
              </div>

              {/* Office Info */}
              <div className="mt-6 p-4 bg-orange-50 rounded-2xl border border-orange-200">
                <h4 className="font-semibold text-gray-900 mb-2">Our Office</h4>
                <p className="text-gray-700 text-sm leading-relaxed">
                  GF1, C Block, Aarthi Towers<br />
                  East Tambaram, Chennai<br />
                  Tamil Nadu, India
                </p>
                <div className="mt-3 flex items-center space-x-4 text-sm">
                  <button
                    onClick={() => window.open('https://www.google.com/maps/place/Mothercode+Software+Systems/@12.9224012,80.1279691,243m/data=!3m2!1e3!4b1!4m6!3m5!1s0x3a525f115336db2d:0xddf5dad187e5558e!8m2!3d12.9224012!4d80.1279691!16s%2Fg%2F11y3k8qx8q?entry=ttu&g_ep=EgoyMDI1MDEyMS4wIKXMDSoASAFQAw%3D%3D', '_blank')}
                    className="flex items-center space-x-1 text-orange-600 font-medium hover:text-orange-700 transition-colors duration-200"
                  >
                    <MapPinIcon className="w-4 h-4" />
                    <span>Get Directions</span>
                  </button>
                  <span className="text-gray-500">•</span>
                  <span className="text-gray-600">Mon-Fri: 9AM - 6PM</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Contact;
