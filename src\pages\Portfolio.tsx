import React from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  CameraIcon,
  TruckIcon,
  CogIcon,
  ScissorsIcon,
  ShieldCheckIcon,
  CloudIcon,
  AcademicCapIcon,
  ArrowRightIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';

const Portfolio: React.FC = () => {
  const projects = [
    {
      id: 1,
      title: "Laser Picking System",
      category: "Warehouse Automation",
      description: "Camera-based QR locator with laser pointer recognition for precise product picking in warehouses. AI-powered system guides humans and robots to exact product locations using computer vision and laser beam guidance.",
      technologies: ["Computer Vision", "QR Recognition", "Laser Guidance", "Warehouse Management"],
      image: "https://www.seaspace-int.com/wp-content/uploads/2021/02/Warehouse-and-Storage-Facilities.png",
      icon: <CameraIcon className="w-8 h-8" />,
      results: ["99.5% picking accuracy", "40% faster operations", "Reduced human errors"],
      caseStudy: {
        client: "Global Warehouse Solutions",
        challenge: "Large warehouses faced significant inefficiencies with manual product picking, leading to frequent errors and slow operations.",
        solution: "Implemented AI-powered laser guidance system with QR code recognition to guide workers to exact product locations.",
        outcome: "Achieved 99.5% picking accuracy and 40% faster operations, dramatically reducing operational costs."
      }
    },
    {
      id: 2,
      title: "Video Content Analysis for Racing",
      category: "Sports Analytics",
      description: "Advanced video analysis system for RC car racing tracks. Predicts car performance, tracks speed, and provides real-time race field analytics for Yokomo, a leading Japanese RC car manufacturer.",
      technologies: ["Video Analytics", "Speed Detection", "Performance Prediction", "Real-time Processing"],
      image: "https://i.ytimg.com/vi/2BSxqPHSWNs/maxresdefault.jpg",
      icon: <TruckIcon className="w-8 h-8" />,
      results: ["Real-time race analytics", "Performance optimization", "Enhanced viewer experience"],
      caseStudy: {
        client: "Yokomo Corporation",
        challenge: "RC racing events needed real-time analytics and performance insights to enhance competitive racing experience.",
        solution: "Developed advanced video analysis system with AI-powered speed detection and performance prediction algorithms.",
        outcome: "Delivered real-time dashboard with comprehensive race analytics, improving both competitor and viewer experience."
      }
    },
    {
      id: 3,
      title: "Digital Picking System",
      category: "Manufacturing",
      description: "Smart picking system for Toyota Japan assembly line. RFID-enabled hand scanners with Bluetooth communication, lamp indicators, and audio feedback ensure correct spare parts selection, dramatically reducing human errors.",
      technologies: ["RFID Technology", "Bluetooth Communication", "IoT Sensors", "Assembly Line Integration"],
      image: "https://jp.sharp/business/dps/solution/dps/images/img_01.jpg",
      icon: <CogIcon className="w-8 h-8" />,
      results: ["60% error reduction", "Increased productivity", "Streamlined assembly process"],
      caseStudy: {
        client: "Toyota Japan",
        challenge: "Assembly line faced significant human errors in spare parts picking, leading to production delays and quality issues.",
        solution: "Implemented RFID-enabled digital picking system with Bluetooth communication, lamp indicators, and audio feedback.",
        outcome: "Achieved 60% reduction in human errors and 40% increase in productivity, streamlining the entire assembly process."
      }
    },
    {
      id: 4,
      title: "AI Textile Measurement",
      category: "Textile Industry",
      description: "Precision cloth measurement system using AI and computer vision for textile factories. Automated fabric dimension analysis ensures accurate measurements and quality control in textile manufacturing.",
      technologies: ["Computer Vision", "AI Measurement", "Quality Control", "Textile Processing"],
      image: "https://t3.ftcdn.net/jpg/02/80/04/92/360_F_280049260_MFShK1dvwxQ5zMKgsvFaIFlGhZHIWgSi.jpg",
      icon: <ScissorsIcon className="w-8 h-8" />,
      results: ["Precise measurements", "Quality assurance", "Automated processing"],
      caseStudy: {
        client: "Textile Manufacturing Corp",
        challenge: "Manual fabric measurement was inconsistent and time-consuming, leading to material waste and quality issues.",
        solution: "Deployed AI-powered computer vision system for automated fabric dimension analysis and quality control.",
        outcome: "Achieved 100% measurement automation with 95% precision improvement, significantly reducing material waste."
      }
    },
    {
      id: 5,
      title: "Tire Damage Detection",
      category: "Automotive Quality Control",
      description: "AI-powered tire quality inspection system for car manufacturing. Automated image processing detects defects and damage before tires are fitted to vehicles, ensuring safety and quality standards.",
      technologies: ["AI Image Processing", "Defect Detection", "Quality Assurance", "Automotive Manufacturing"],
      image: "https://logos3pl.com/wp-content/uploads/2024/01/tire-tyre-logistics-services-industry-overview-logos-logistics-1024x683.jpg",
      icon: <ShieldCheckIcon className="w-8 h-8" />,
      results: ["100% quality screening", "Defect prevention", "Enhanced safety standards"],
      caseStudy: {
        client: "Automotive Manufacturing Ltd",
        challenge: "Manual tire inspection was slow and couldn't guarantee 100% defect detection, risking safety standards.",
        solution: "Implemented AI-powered image processing system for automated tire damage detection and quality screening.",
        outcome: "Achieved 100% quality screening with 98.5% defect detection accuracy, ensuring enhanced safety standards."
      }
    },
    {
      id: 6,
      title: "PLC & Circuit Board Detection",
      category: "Electronics Manufacturing",
      description: "Conveyor-based AI system for continuous circuit board inspection. Real-time processing ensures relays and circuits are correctly positioned inside boxes using advanced image processing technology.",
      technologies: ["Conveyor Integration", "Real-time Processing", "Circuit Analysis", "Quality Control"],
      image: "https://hackster.imgix.net/uploads/attachments/1348144/_ijhOT5UeAU.blob?auto=compress&w=900&h=675&fit=min&fm=jpg",
      icon: <ShieldCheckIcon className="w-8 h-8" />,
      results: ["Continuous monitoring", "Real-time detection", "Quality assurance"],
      caseStudy: {
        client: "Electronics Manufacturer Inc",
        challenge: "Manual circuit board inspection was slow and couldn't keep up with high-speed production lines.",
        solution: "Developed conveyor-integrated AI system for continuous, real-time circuit board inspection and quality control.",
        outcome: "Enabled 24/7 monitoring with 99% inspection accuracy, matching production line speed requirements."
      }
    },
    {
      id: 7,
      title: "AI Cloud Log Tracker",
      category: "Cloud Security",
      description: "Intelligent log tracking system for Azure and AWS with AI knowledge graph. Users can easily query and check security logs using natural language AI interface for enhanced cloud monitoring.",
      technologies: ["Azure Integration", "AWS Services", "AI Knowledge Graph", "Security Monitoring"],
      image: "https://images.prismic.io/qovery/Zw_ZWoF3NbkBXjEY_CloudProviderComparison-1-.jpg?ixlib=gatsbyFP&auto=format%2Ccompress&fit=max&rect=0%2C27%2C1214%2C637&w=1200&h=630",
      icon: <CloudIcon className="w-8 h-8" />,
      results: ["Enhanced security monitoring", "Natural language queries", "Intelligent insights"],
      caseStudy: {
        client: "Enterprise Cloud Solutions",
        challenge: "Complex cloud infrastructure logs were difficult to analyze, making security monitoring inefficient.",
        solution: "Built AI knowledge graph system enabling natural language queries for Azure and AWS security logs.",
        outcome: "Transformed security monitoring with intelligent insights and 70% faster threat detection."
      }
    },
    {
      id: 8,
      title: "AI Education Agent",
      category: "Education Technology",
      description: "Intelligent agent system for educational institutions managing admissions and institutional data. AI-powered assistant handles common queries and streamlines administrative processes.",
      technologies: ["AI Agents", "Educational Data Management", "Admission Processing", "Institutional Intelligence"],
      image: "https://wp.sfdcdigital.com/en-us/wp-content/uploads/sites/4/2024/08/AICopilotVsChatbot_02.webp?w=1024",
      icon: <AcademicCapIcon className="w-8 h-8" />,
      results: ["Streamlined admissions", "Automated responses", "Enhanced efficiency"],
      caseStudy: {
        client: "Education Institute",
        challenge: "Manual admission processes and student queries were overwhelming administrative staff.",
        solution: "Implemented AI agent system for automated admission processing and intelligent query handling.",
        outcome: "Reduced administrative workload by 60% while improving response time and student satisfaction."
      }
    },
    {
      id: 9,
      title: "Line Management System (LMS)",
      category: "Factory Management",
      description: "Advanced drag-and-drop roster management system for factory operations. Track employee assignments, monitor in/out times, manage breaks, and analyze performance efficiency with intuitive visual interface.",
      technologies: ["Drag & Drop Interface", "Time Tracking", "Performance Analytics", "Factory Management"],
      image: "https://www.shiftbase.com/hubfs/Manager%20working%20on%20Shiftbase%20scheduling%20tool%20to%20create%20employee%20schedules.png",
      icon: <CogIcon className="w-8 h-8" />,
      results: ["90% faster scheduling", "Real-time tracking", "Enhanced productivity"],
      caseStudy: {
        client: "Manufacturing Operations Ltd",
        challenge: "Manual roster management and time tracking was inefficient, leading to scheduling conflicts and productivity issues.",
        solution: "Developed intuitive drag-and-drop LMS with real-time tracking, break management, and performance analytics.",
        outcome: "Achieved 90% faster scheduling, eliminated conflicts, and improved overall factory productivity by 35%."
      }
    },
    {
      id: 10,
      title: "Queue Management System",
      category: "Service Management",
      description: "Intelligent queue management system that optimizes customer flow, reduces wait times, and enhances service delivery through real-time monitoring and automated notifications.",
      technologies: ["Queue Optimization", "Real-time Monitoring", "SMS/Email Notifications", "Analytics Dashboard"],
      image: "https://movingtactics.co.za/wp-content/uploads/2024/05/Customers-waiting-near-a-queue-kiosk-and-screen-01-1024x751.png",
      icon: <CogIcon className="w-8 h-8" />,
      results: ["75% reduced wait times", "Automated notifications", "Enhanced customer satisfaction"],
      caseStudy: {
        client: "Service Center Solutions",
        challenge: "Long customer wait times and inefficient service delivery were causing customer dissatisfaction and operational bottlenecks.",
        solution: "Implemented intelligent queue management with real-time monitoring, automated notifications, and service optimization algorithms.",
        outcome: "Reduced average wait times by 75%, improved customer satisfaction by 60%, and increased service efficiency by 45%."
      }
    },
    {
      id: 11,
      title: "Appointment Management System",
      category: "Healthcare Technology",
      description: "Comprehensive appointment scheduling system with automated reminders, calendar integration, and patient management features designed for healthcare providers and service businesses.",
      technologies: ["Appointment Scheduling", "Calendar Integration", "Automated Reminders", "Patient Management"],
      image: "https://www.bookingpressplugin.com/wp-content/uploads/2023/12/Steps-to-Create-a-Patient-Appointment-Scheduling-System-Banner.webp",
      icon: <CalendarIcon className="w-8 h-8" />,
      results: ["95% appointment accuracy", "Automated scheduling", "Reduced no-shows"],
      caseStudy: {
        client: "Healthcare Providers Network",
        challenge: "Manual appointment scheduling led to double bookings, missed appointments, and administrative overhead consuming valuable staff time.",
        solution: "Developed comprehensive appointment management system with automated scheduling, reminders, and calendar integration.",
        outcome: "Achieved 95% appointment accuracy, reduced no-shows by 70%, and decreased administrative workload by 80%."
      }
    },
    {
      id: 12,
      title: "Logistics Data Tracker",
      category: "Supply Chain Management",
      description: "Advanced logistics tracking application with QR reader and camera capture for operators to efficiently track product locations, load/unload operations in containers and warehouses.",
      technologies: ["QR Code Scanner", "Camera Integration", "Location Tracking", "Warehouse Management"],
      image: "https://img.freepik.com/premium-photo/delivery-logistics-woman-tablet-container-inspection-with-stock-cargo-inventory-checklist-warehouse-industrial-supply-chain-worker-working-distribution-customs-job-outdoors_590464-88544.jpg",
      icon: <TruckIcon className="w-8 h-8" />,
      results: ["Real-time tracking", "99% accuracy", "Streamlined operations"],
      caseStudy: {
        client: "Global Logistics Corp",
        challenge: "Manual tracking of products in containers and warehouses led to misplaced items, inefficient operations, and lack of real-time visibility.",
        solution: "Built logistics data tracker with QR scanning, camera capture, and real-time location tracking for operators.",
        outcome: "Achieved 99% tracking accuracy, eliminated lost products, and improved operational efficiency by 65%."
      }
    }
  ];

  return (
    <div className="pt-20 min-h-screen bg-gradient-to-br from-orange-50 via-white to-amber-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-heading font-bold text-gray-900 mb-6 tracking-tight">
            Our <span className="bg-gradient-to-r from-orange-600 to-amber-700 bg-clip-text text-transparent">Portfolio</span>
          </h1>
          <p className="text-xl text-gray-600 leading-relaxed max-w-4xl mx-auto font-medium">
            Discover how we've transformed businesses across industries with cutting-edge AI, automation, and intelligent systems.
            Real projects, measurable results, satisfied clients.
          </p>
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project) => (
            <div
              key={project.id}
              className="group bg-white/80 backdrop-blur-sm rounded-3xl overflow-hidden border border-orange-200 hover:border-orange-400 hover:shadow-xl transition-all duration-300"
            >
              {/* Project Image */}
              <div className="relative h-48 overflow-hidden">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                <div className="absolute top-4 left-4 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-xl flex items-center justify-center text-orange-600 shadow-lg">
                  {project.icon}
                </div>
                <div className="absolute bottom-4 left-4 right-4">
                  <span className="inline-block px-3 py-1 bg-orange-600 text-white text-xs font-semibold rounded-full">
                    {project.category}
                  </span>
                </div>
              </div>

              {/* Project Content */}
              <div className="p-6">
                <h3 className="text-xl font-heading font-bold text-gray-900 mb-3 group-hover:text-orange-600 transition-colors duration-200">
                  {project.title}
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed mb-4">
                  {project.description}
                </p>

                {/* Technologies */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {project.technologies.slice(0, 3).map((tech, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-orange-100 text-orange-700 text-xs font-medium rounded-md"
                    >
                      {tech}
                    </span>
                  ))}
                  {project.technologies.length > 3 && (
                    <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs font-medium rounded-md">
                      +{project.technologies.length - 3} more
                    </span>
                  )}
                </div>

                {/* Results */}
                <div className="space-y-1 mb-4">
                  <h4 className="text-sm font-semibold text-gray-900 mb-2">Key Results:</h4>
                  {project.results.map((result, index) => (
                    <div key={index} className="flex items-center text-xs text-gray-600">
                      <div className="w-1.5 h-1.5 bg-orange-500 rounded-full mr-2"></div>
                      {result}
                    </div>
                  ))}
                </div>

                {/* View Case Studies Button */}
                <Link
                  to={`/case-study/${project.id}`}
                  className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-600 to-amber-700 text-white text-sm font-semibold rounded-xl hover:shadow-lg transition-all duration-300"
                >
                  View Case Study
                  <ArrowRightIcon className="w-4 h-4" />
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Portfolio;
