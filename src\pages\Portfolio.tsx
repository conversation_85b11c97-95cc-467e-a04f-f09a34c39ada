import React from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  CameraIcon,
  TruckIcon,
  CogIcon,
  ScissorsIcon,
  ShieldCheckIcon,
  CloudIcon,
  AcademicCapIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';

const Portfolio: React.FC = () => {
  const projects = [
    {
      id: 1,
      title: "Laser Picking System",
      category: "Warehouse Automation",
      description: "Camera-based QR locator with laser pointer recognition for precise product picking in warehouses. AI-powered system guides humans and robots to exact product locations using computer vision and laser beam guidance.",
      technologies: ["Computer Vision", "QR Recognition", "Laser Guidance", "Warehouse Management"],
      image: "https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?w=600&h=400&fit=crop",
      icon: <CameraIcon className="w-8 h-8" />,
      results: ["99.5% picking accuracy", "40% faster operations", "Reduced human errors"],
      caseStudy: {
        client: "Global Warehouse Solutions",
        challenge: "Large warehouses faced significant inefficiencies with manual product picking, leading to frequent errors and slow operations.",
        solution: "Implemented AI-powered laser guidance system with QR code recognition to guide workers to exact product locations.",
        outcome: "Achieved 99.5% picking accuracy and 40% faster operations, dramatically reducing operational costs."
      }
    },
    {
      id: 2,
      title: "Video Content Analysis for Racing",
      category: "Sports Analytics",
      description: "Advanced video analysis system for RC car racing tracks. Predicts car performance, tracks speed, and provides real-time race field analytics for Yokomo, a leading Japanese RC car manufacturer.",
      technologies: ["Video Analytics", "Speed Detection", "Performance Prediction", "Real-time Processing"],
      image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=600&h=400&fit=crop",
      icon: <TruckIcon className="w-8 h-8" />,
      results: ["Real-time race analytics", "Performance optimization", "Enhanced viewer experience"],
      caseStudy: {
        client: "Yokomo Corporation",
        challenge: "RC racing events needed real-time analytics and performance insights to enhance competitive racing experience.",
        solution: "Developed advanced video analysis system with AI-powered speed detection and performance prediction algorithms.",
        outcome: "Delivered real-time dashboard with comprehensive race analytics, improving both competitor and viewer experience."
      }
    },
    {
      id: 3,
      title: "Digital Picking System (DPS)",
      category: "Manufacturing",
      description: "Smart picking system for Toyota Japan assembly line. RFID-enabled hand scanners with Bluetooth communication, lamp indicators, and audio feedback ensure correct spare parts selection, dramatically reducing human errors.",
      technologies: ["RFID Technology", "Bluetooth Communication", "IoT Sensors", "Assembly Line Integration"],
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=600&h=400&fit=crop",
      icon: <CogIcon className="w-8 h-8" />,
      results: ["60% error reduction", "Increased productivity", "Streamlined assembly process"],
      caseStudy: {
        client: "Toyota Japan",
        challenge: "Assembly line faced significant human errors in spare parts picking, leading to production delays and quality issues.",
        solution: "Implemented RFID-enabled digital picking system with Bluetooth communication, lamp indicators, and audio feedback.",
        outcome: "Achieved 60% reduction in human errors and 40% increase in productivity, streamlining the entire assembly process."
      }
    },
    {
      id: 4,
      title: "AI Textile Measurement",
      category: "Textile Industry",
      description: "Precision cloth measurement system using AI and computer vision for textile factories. Automated fabric dimension analysis ensures accurate measurements and quality control in textile manufacturing.",
      technologies: ["Computer Vision", "AI Measurement", "Quality Control", "Textile Processing"],
      image: "https://images.unsplash.com/photo-1558769132-cb1aea458c5e?w=600&h=400&fit=crop",
      icon: <ScissorsIcon className="w-8 h-8" />,
      results: ["Precise measurements", "Quality assurance", "Automated processing"],
      caseStudy: {
        client: "Textile Manufacturing Corp",
        challenge: "Manual fabric measurement was inconsistent and time-consuming, leading to material waste and quality issues.",
        solution: "Deployed AI-powered computer vision system for automated fabric dimension analysis and quality control.",
        outcome: "Achieved 100% measurement automation with 95% precision improvement, significantly reducing material waste."
      }
    },
    {
      id: 5,
      title: "Tire Damage Detection",
      category: "Automotive Quality Control",
      description: "AI-powered tire quality inspection system for car manufacturing. Automated image processing detects defects and damage before tires are fitted to vehicles, ensuring safety and quality standards.",
      technologies: ["AI Image Processing", "Defect Detection", "Quality Assurance", "Automotive Manufacturing"],
      image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=600&h=400&fit=crop",
      icon: <ShieldCheckIcon className="w-8 h-8" />,
      results: ["100% quality screening", "Defect prevention", "Enhanced safety standards"],
      caseStudy: {
        client: "Automotive Manufacturing Ltd",
        challenge: "Manual tire inspection was slow and couldn't guarantee 100% defect detection, risking safety standards.",
        solution: "Implemented AI-powered image processing system for automated tire damage detection and quality screening.",
        outcome: "Achieved 100% quality screening with 98.5% defect detection accuracy, ensuring enhanced safety standards."
      }
    },
    {
      id: 6,
      title: "PLC & Circuit Board Detection",
      category: "Electronics Manufacturing",
      description: "Conveyor-based AI system for continuous circuit board inspection. Real-time processing ensures relays and circuits are correctly positioned inside boxes using advanced image processing technology.",
      technologies: ["Conveyor Integration", "Real-time Processing", "Circuit Analysis", "Quality Control"],
      image: "https://images.unsplash.com/photo-1518770660439-4636190af475?w=600&h=400&fit=crop",
      icon: <ShieldCheckIcon className="w-8 h-8" />,
      results: ["Continuous monitoring", "Real-time detection", "Quality assurance"],
      caseStudy: {
        client: "Electronics Manufacturer Inc",
        challenge: "Manual circuit board inspection was slow and couldn't keep up with high-speed production lines.",
        solution: "Developed conveyor-integrated AI system for continuous, real-time circuit board inspection and quality control.",
        outcome: "Enabled 24/7 monitoring with 99% inspection accuracy, matching production line speed requirements."
      }
    },
    {
      id: 7,
      title: "AI Cloud Log Tracker",
      category: "Cloud Security",
      description: "Intelligent log tracking system for Azure and AWS with AI knowledge graph. Users can easily query and check security logs using natural language AI interface for enhanced cloud monitoring.",
      technologies: ["Azure Integration", "AWS Services", "AI Knowledge Graph", "Security Monitoring"],
      image: "https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=600&h=400&fit=crop",
      icon: <CloudIcon className="w-8 h-8" />,
      results: ["Enhanced security monitoring", "Natural language queries", "Intelligent insights"],
      caseStudy: {
        client: "Enterprise Cloud Solutions",
        challenge: "Complex cloud infrastructure logs were difficult to analyze, making security monitoring inefficient.",
        solution: "Built AI knowledge graph system enabling natural language queries for Azure and AWS security logs.",
        outcome: "Transformed security monitoring with intelligent insights and 70% faster threat detection."
      }
    },
    {
      id: 8,
      title: "AI Education Agent",
      category: "Education Technology",
      description: "Intelligent agent system for educational institutions managing admissions and institutional data. AI-powered assistant handles common queries and streamlines administrative processes.",
      technologies: ["AI Agents", "Educational Data Management", "Admission Processing", "Institutional Intelligence"],
      image: "https://images.unsplash.com/photo-1523240795612-9a054b0db644?w=600&h=400&fit=crop",
      icon: <AcademicCapIcon className="w-8 h-8" />,
      results: ["Streamlined admissions", "Automated responses", "Enhanced efficiency"],
      caseStudy: {
        client: "Education Institute",
        challenge: "Manual admission processes and student queries were overwhelming administrative staff.",
        solution: "Implemented AI agent system for automated admission processing and intelligent query handling.",
        outcome: "Reduced administrative workload by 60% while improving response time and student satisfaction."
      }
    },
    {
      id: 9,
      title: "Line Management System (LMS)",
      category: "Factory Management",
      description: "Advanced drag-and-drop roster management system for factory operations. Track employee assignments, monitor in/out times, manage breaks, and analyze performance efficiency with intuitive visual interface.",
      technologies: ["Drag & Drop Interface", "Time Tracking", "Performance Analytics", "Factory Management"],
      image: "https://images.unsplash.com/photo-1565793298595-6a879b1d9492?w=600&h=400&fit=crop",
      icon: <CogIcon className="w-8 h-8" />,
      results: ["90% faster scheduling", "Real-time tracking", "Enhanced productivity"],
      caseStudy: {
        client: "Manufacturing Operations Ltd",
        challenge: "Manual roster management and time tracking was inefficient, leading to scheduling conflicts and productivity issues.",
        solution: "Developed intuitive drag-and-drop LMS with real-time tracking, break management, and performance analytics.",
        outcome: "Achieved 90% faster scheduling, eliminated conflicts, and improved overall factory productivity by 35%."
      }
    }
  ];

  return (
    <div className="pt-20 min-h-screen bg-gradient-to-br from-orange-50 via-white to-amber-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-heading font-bold text-gray-900 mb-6 tracking-tight">
            Our <span className="bg-gradient-to-r from-orange-600 to-amber-700 bg-clip-text text-transparent">Portfolio</span>
          </h1>
          <p className="text-xl text-gray-600 leading-relaxed max-w-4xl mx-auto font-medium">
            Discover how we've transformed businesses across industries with cutting-edge AI, automation, and intelligent systems.
            Real projects, measurable results, satisfied clients.
          </p>
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project) => (
            <div
              key={project.id}
              className="group bg-white/80 backdrop-blur-sm rounded-3xl overflow-hidden border border-orange-200 hover:border-orange-400 hover:shadow-xl transition-all duration-300"
            >
              {/* Project Image */}
              <div className="relative h-48 overflow-hidden">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                <div className="absolute top-4 left-4 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-xl flex items-center justify-center text-orange-600 shadow-lg">
                  {project.icon}
                </div>
                <div className="absolute bottom-4 left-4 right-4">
                  <span className="inline-block px-3 py-1 bg-orange-600 text-white text-xs font-semibold rounded-full">
                    {project.category}
                  </span>
                </div>
              </div>

              {/* Project Content */}
              <div className="p-6">
                <h3 className="text-xl font-heading font-bold text-gray-900 mb-3 group-hover:text-orange-600 transition-colors duration-200">
                  {project.title}
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed mb-4">
                  {project.description}
                </p>

                {/* Technologies */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {project.technologies.slice(0, 3).map((tech, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-orange-100 text-orange-700 text-xs font-medium rounded-md"
                    >
                      {tech}
                    </span>
                  ))}
                  {project.technologies.length > 3 && (
                    <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs font-medium rounded-md">
                      +{project.technologies.length - 3} more
                    </span>
                  )}
                </div>

                {/* Results */}
                <div className="space-y-1 mb-4">
                  <h4 className="text-sm font-semibold text-gray-900 mb-2">Key Results:</h4>
                  {project.results.map((result, index) => (
                    <div key={index} className="flex items-center text-xs text-gray-600">
                      <div className="w-1.5 h-1.5 bg-orange-500 rounded-full mr-2"></div>
                      {result}
                    </div>
                  ))}
                </div>

                {/* View Case Studies Button */}
                <Link
                  to={`/case-study/${project.id}`}
                  className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-600 to-amber-700 text-white text-sm font-semibold rounded-xl hover:shadow-lg transition-all duration-300"
                >
                  View Case Study
                  <ArrowRightIcon className="w-4 h-4" />
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="inline-flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-8 p-8 bg-gradient-to-r from-gray-900 to-gray-800 rounded-3xl text-white shadow-xl">
            <div className="text-center sm:text-left">
              <h4 className="text-2xl font-heading font-bold mb-2">Ready to Start Your Success Story?</h4>
              <p className="text-gray-300">Let's discuss how we can transform your business with proven solutions.</p>
            </div>
            <div className="flex space-x-4">
              <Link
                to="/contact"
                className="px-6 py-3 bg-gradient-to-r from-orange-600 to-amber-700 text-white rounded-xl font-semibold hover:shadow-lg transition-all duration-300"
              >
                Start Your Project
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Portfolio;
