{"name": "mothercode", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.2.0", "@lottiefiles/dotlottie-react": "^0.14.3", "@tailwindcss/vite": "^4.1.11", "@types/react-router-dom": "^5.3.3", "clsx": "^2.1.1", "country-flag-icons": "^1.5.19", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-router-dom": "^7.7.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}