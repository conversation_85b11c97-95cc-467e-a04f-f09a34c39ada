import React from 'react';
import { DotLottieReact } from '@lottiefiles/dotlottie-react';
import { 
  CheckCircleIcon, 
  UserGroupIcon, 
  ShieldCheckIcon, 
  HeartIcon,
  ChatBubbleLeftRightIcon,
  CogIcon
} from '@heroicons/react/24/outline';

const DevelopmentProcess: React.FC = () => {
  const processSteps = [
    {
      icon: <UserGroupIcon className="w-6 h-6" />,
      title: "Agile Collaboration",
      description: "We work closely with you using agile methodologies, ensuring transparency and flexibility throughout the development process."
    },
    {
      icon: <CogIcon className="w-6 h-6" />,
      title: "Iterative Development",
      description: "Our sprint-based approach delivers working software incrementally, allowing for continuous feedback and improvements."
    },
    {
      icon: <ChatBubbleLeftRightIcon className="w-6 h-6" />,
      title: "Continuous Communication",
      description: "Regular stand-ups, reviews, and retrospectives keep you informed and involved in every step of the journey."
    },
    {
      icon: <CheckCircleIcon className="w-6 h-6" />,
      title: "Quality Assurance",
      description: "Rigorous testing and code reviews ensure we deliver robust, scalable solutions that meet your business needs."
    }
  ];

  const carePoints = [
    {
      icon: <HeartIcon className="w-5 h-5" />,
      title: "Personalised Support",
      description: "Every client receives dedicated attention tailored to their unique requirements and business goals."
    },
    {
      icon: <ShieldCheckIcon className="w-5 h-5" />,
      title: "Honouring Your Choices",
      description: "We respect your decisions and preferences, ensuring our solutions align with your vision and values."
    },
    {
      icon: <UserGroupIcon className="w-5 h-5" />,
      title: "Ethical Data Handling",
      description: "Your data is handled with the utmost care, following industry best practices and ethical standards."
    }
  ];

  return (
    <section className="py-16 sm:py-20 md:py-24 bg-gradient-to-br from-white via-orange-50 to-amber-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-orange-600 to-amber-700 text-white rounded-full text-sm font-medium mb-8 shadow-lg">
            <span className="w-3 h-3 bg-white rounded-full mr-3 animate-pulse"></span>
            Modern Development Approach
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-heading font-extrabold text-gray-900 mb-6 leading-tight tracking-tight">
         A <span className="bg-gradient-to-r from-orange-600 to-amber-700 bg-clip-text text-transparent">Process</span> Built Around You
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto font-medium">
            We follow modern agile methodologies to deliver exceptional software solutions while maintaining the highest standards of care and professionalism.
          </p>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center mb-20">
          {/* Left side - Lottie Animation */}
          <div className="order-2 lg:order-1">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-orange-200 to-amber-200 rounded-3xl blur-3xl opacity-30 animate-pulse"></div>
              <div className="relative">
                <DotLottieReact
                  src="/src/animations/BusinessTeamDiscusses.lottie"
                  loop
                  autoplay
                  className="w-full h-auto max-w-lg mx-auto"
                />
              </div>
            </div>
          </div>

          {/* Right side - Development Process */}
          <div className="order-1 lg:order-2">
            <h3 className="text-2xl sm:text-3xl font-heading font-bold text-gray-900 mb-8 tracking-tight">
              Agile Development Methodology
            </h3>
            <div className="space-y-6">
              {processSteps.map((step, index) => (
                <div 
                  key={index}
                  className="flex items-start space-x-4 p-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-orange-200 hover:border-orange-400 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-orange-500 to-amber-600 rounded-xl flex items-center justify-center text-white shadow-md">
                    {step.icon}
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">{step.title}</h4>
                    <p className="text-gray-600 leading-relaxed">{step.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Business With Us Section */}
        <div className="relative">
          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-r from-orange-100 to-amber-100 rounded-3xl opacity-50"></div>
          
          <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 sm:p-12 border border-orange-200 shadow-xl">
            <div className="text-center mb-12">
              <h3 className="text-3xl sm:text-4xl font-heading font-bold text-gray-900 mb-4 tracking-tight">
                Business <span className="bg-gradient-to-r from-orange-600 to-amber-700 bg-clip-text text-transparent">With Us</span>
              </h3>
              <div className="flex items-center justify-center space-x-2 mb-6">
                <HeartIcon className="w-8 h-8 text-orange-500" />
                <h4 className="text-2xl font-semibold text-orange-700">We Care About You</h4>
              </div>
            </div>

            {/* Care Points Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {carePoints.map((point, index) => (
                <div 
                  key={index}
                  className="text-center p-6 rounded-2xl bg-gradient-to-br from-orange-50 to-amber-50 border border-orange-200 hover:border-orange-400 hover:shadow-lg transition-all duration-300"
                >
                  <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-amber-600 rounded-2xl flex items-center justify-center text-white mx-auto mb-4 shadow-md">
                    {point.icon}
                  </div>
                  <h5 className="text-xl font-semibold text-gray-900 mb-3">{point.title}</h5>
                  <p className="text-gray-600 leading-relaxed">{point.description}</p>
                </div>
              ))}
            </div>

          </div>
        </div>
      </div>
    </section>
  );
};

export default DevelopmentProcess;
