import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import {
  ArrowLeftIcon,
  BuildingOfficeIcon,
  CheckCircleIcon,
  LightBulbIcon,
  ChartBarIcon,
  UserGroupIcon,
  TrophyIcon
} from '@heroicons/react/24/outline';

const CaseStudy: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  
  // Case study data - in a real app, this would come from an API
  const caseStudies: { [key: string]: any } = {
    '1': {
      id: 1,
      title: "Laser Picking System",
      client: "Global Warehouse Solutions",
      category: "Warehouse Automation",
      image: "https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?w=800&h=500&fit=crop",
      overview: "Revolutionizing warehouse operations with AI-powered laser guidance system that combines computer vision, QR code recognition, and precision laser pointing to eliminate picking errors and dramatically improve operational efficiency.",
      challenge: {
        title: "The Challenge",
        description: "Global Warehouse Solutions operated multiple large-scale distribution centers handling thousands of SKUs daily. Their manual picking process was plagued with human errors, leading to incorrect shipments, customer complaints, and significant operational costs. Workers struggled to locate products quickly in vast warehouse spaces, resulting in productivity bottlenecks and increased labor costs.",
        painPoints: [
          "15% error rate in manual picking operations",
          "Average 45 minutes per picking route completion",
          "High training costs for new warehouse staff",
          "Customer complaints due to incorrect shipments",
          "Significant costs from returns and re-shipments"
        ]
      },
      solution: {
        title: "Our Solution",
        description: "We developed an integrated AI-powered laser picking system that combines multiple cutting-edge technologies to create a seamless, error-free picking experience.",
        approach: [
          {
            phase: "Phase 1: System Analysis & Design",
            description: "Comprehensive warehouse mapping and workflow analysis to design optimal laser guidance paths",
            duration: "2 months"
          },
          {
            phase: "Phase 2: AI Vision Development",
            description: "Development of computer vision algorithms for real-time QR code recognition and product identification",
            duration: "3 months"
          },
          {
            phase: "Phase 3: Laser Integration",
            description: "Integration of precision laser guidance system with warehouse management software",
            duration: "2 months"
          },
          {
            phase: "Phase 4: Testing & Deployment",
            description: "Comprehensive testing, staff training, and full system deployment across facilities",
            duration: "1 month"
          }
        ],
        technologies: [
          "Computer Vision & AI",
          "QR Code Recognition",
          "Precision Laser Guidance",
          "Warehouse Management Integration",
          "Real-time Processing",
          "IoT Sensors"
        ]
      },
      results: {
        title: "Measurable Results",
        description: "The implementation delivered exceptional results that exceeded client expectations and transformed their warehouse operations.",
        metrics: [
          {
            metric: "Picking Accuracy",
            before: "85%",
            after: "99.5%",
            improvement: "+14.5%"
          },
          {
            metric: "Operation Speed",
            before: "45 min/route",
            after: "27 min/route",
            improvement: "40% faster"
          },
          {
            metric: "Training Time",
            before: "2 weeks",
            after: "3 days",
            improvement: "85% reduction"
          },
          {
            metric: "Error Costs",
            before: "$50K/month",
            after: "$2K/month",
            improvement: "96% reduction"
          }
        ],
        benefits: [
          "Eliminated 99% of picking errors",
          "Reduced operational costs by $576K annually",
          "Improved customer satisfaction scores by 35%",
          "Decreased new employee training time by 85%",
          "Enabled 24/7 operations with consistent accuracy"
        ]
      },
      testimonial: {
        quote: "Mothercode's laser picking system has completely transformed our warehouse operations. The accuracy improvements and cost savings exceeded our most optimistic projections. Our team adapted quickly, and customer satisfaction has never been higher.",
        author: "Sarah Johnson",
        position: "Operations Director",
        company: "Global Warehouse Solutions"
      }
    },
    '2': {
      id: 2,
      title: "Video Content Analysis for Racing",
      client: "Yokomo Corporation",
      category: "Sports Analytics",
      image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=500&fit=crop",
      overview: "Advanced AI-powered video analysis system for RC car racing that provides real-time performance insights, speed tracking, and predictive analytics to enhance competitive racing experience.",
      challenge: {
        title: "The Challenge",
        description: "Yokomo Corporation, a leading Japanese RC car manufacturer, needed sophisticated analytics for their racing events. Traditional timing systems provided limited insights, and manual analysis was time-consuming and subjective.",
        painPoints: [
          "Limited real-time performance data during races",
          "Manual video analysis taking days to complete",
          "Lack of predictive insights for race strategy",
          "Difficulty in identifying performance optimization opportunities",
          "No standardized metrics for comparing different racing conditions"
        ]
      },
      solution: {
        title: "Our Solution",
        description: "We developed a comprehensive video analysis platform using advanced AI algorithms to provide real-time racing insights and performance optimization recommendations.",
        approach: [
          {
            phase: "Phase 1: Requirements Analysis",
            description: "Deep dive into racing dynamics and performance metrics identification",
            duration: "1 month"
          },
          {
            phase: "Phase 2: AI Model Development",
            description: "Development of computer vision models for speed detection and performance analysis",
            duration: "3 months"
          },
          {
            phase: "Phase 3: Real-time Processing",
            description: "Implementation of real-time video processing and analytics dashboard",
            duration: "1.5 months"
          },
          {
            phase: "Phase 4: Testing & Optimization",
            description: "Extensive testing with live racing events and system optimization",
            duration: "0.5 months"
          }
        ],
        technologies: [
          "Computer Vision",
          "AI/ML Algorithms",
          "Real-time Video Processing",
          "Predictive Analytics",
          "Performance Optimization",
          "Dashboard Development"
        ]
      },
      results: {
        title: "Exceptional Outcomes",
        description: "The system revolutionized how Yokomo approaches RC racing analytics and performance optimization.",
        metrics: [
          {
            metric: "Analysis Time",
            before: "3 days",
            after: "Real-time",
            improvement: "100% faster"
          },
          {
            metric: "Data Accuracy",
            before: "70%",
            after: "98%",
            improvement: "+28%"
          },
          {
            metric: "Performance Insights",
            before: "5 metrics",
            after: "25+ metrics",
            improvement: "5x more data"
          }
        ],
        benefits: [
          "Real-time race analytics and insights",
          "Improved race strategy development",
          "Enhanced viewer engagement experience",
          "Standardized performance benchmarking",
          "Predictive maintenance recommendations"
        ]
      },
      testimonial: {
        quote: "The video analysis system has elevated our racing events to a professional level. Real-time insights help racers improve their performance, and spectators love the detailed analytics dashboard.",
        author: "Hiroshi Tanaka",
        position: "Racing Division Manager",
        company: "Yokomo Corporation"
      }
    },
    '3': {
      id: 3,
      title: "Digital Picking System (DPS)",
      client: "Toyota Japan",
      category: "Manufacturing",
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=800&h=500&fit=crop",
      overview: "Revolutionary RFID-enabled digital picking system for Toyota's assembly line that combines Bluetooth communication, visual indicators, and audio feedback to eliminate human errors and streamline manufacturing processes.",
      challenge: {
        title: "The Manufacturing Challenge",
        description: "Toyota Japan's assembly line faced significant challenges with manual spare parts picking, leading to production delays, quality issues, and increased operational costs. The complexity of their manufacturing process required precision that manual systems couldn't consistently deliver.",
        painPoints: [
          "15% human error rate in parts selection",
          "Production line delays due to incorrect parts",
          "Quality control issues affecting final products",
          "High training costs for assembly line workers",
          "Inefficient inventory management and tracking"
        ]
      },
      solution: {
        title: "Smart Manufacturing Solution",
        description: "We developed an integrated RFID-enabled digital picking system that revolutionizes how assembly line workers select and verify spare parts, ensuring 100% accuracy and seamless integration with existing manufacturing processes.",
        approach: [
          {
            phase: "Phase 1: Manufacturing Analysis",
            description: "Comprehensive study of assembly line workflows and parts management processes",
            duration: "1.5 months"
          },
          {
            phase: "Phase 2: RFID System Development",
            description: "Development of RFID-enabled hand scanners with Bluetooth communication protocols",
            duration: "3 months"
          },
          {
            phase: "Phase 3: Integration & Testing",
            description: "Integration with existing manufacturing systems and comprehensive testing",
            duration: "2.5 months"
          },
          {
            phase: "Phase 4: Deployment & Training",
            description: "Full system deployment and comprehensive staff training program",
            duration: "1 month"
          }
        ],
        technologies: [
          "RFID Technology",
          "Bluetooth Communication",
          "IoT Sensors",
          "Assembly Line Integration",
          "Real-time Tracking",
          "Audio-Visual Feedback"
        ]
      },
      results: {
        title: "Manufacturing Excellence Achieved",
        description: "The digital picking system transformed Toyota's assembly line operations, delivering unprecedented accuracy and efficiency improvements that exceeded all expectations.",
        metrics: [
          {
            metric: "Error Rate",
            before: "15%",
            after: "0.1%",
            improvement: "99.3% reduction"
          },
          {
            metric: "Productivity",
            before: "100 units/hour",
            after: "140 units/hour",
            improvement: "40% increase"
          },
          {
            metric: "Training Time",
            before: "10 days",
            after: "2 days",
            improvement: "80% reduction"
          },
          {
            metric: "Quality Issues",
            before: "8%",
            after: "0.5%",
            improvement: "94% reduction"
          }
        ],
        benefits: [
          "Eliminated 99% of picking errors",
          "Increased assembly line productivity by 40%",
          "Reduced quality control issues by 94%",
          "Streamlined worker training process",
          "Real-time inventory tracking and management"
        ]
      },
      testimonial: {
        quote: "Mothercode's digital picking system has revolutionized our assembly line operations. The dramatic reduction in errors and increase in productivity has exceeded our expectations. Our workers love the intuitive system, and our quality metrics have never been better.",
        author: "Kenji Yamamoto",
        position: "Manufacturing Director",
        company: "Toyota Japan"
      }
    },
    '4': {
      id: 4,
      title: "AI Textile Measurement",
      client: "Textile Manufacturing Corp",
      category: "Textile Industry",
      image: "https://images.unsplash.com/photo-1558769132-cb1aea458c5e?w=800&h=500&fit=crop",
      overview: "Precision AI-powered cloth measurement system that revolutionizes textile manufacturing through automated fabric dimension analysis, ensuring consistent quality and eliminating material waste.",
      challenge: {
        title: "Textile Industry Challenge",
        description: "Manual fabric measurement processes were inconsistent, time-consuming, and led to significant material waste. The textile industry required precision that human measurement couldn't consistently deliver.",
        painPoints: [
          "20% variance in manual measurements",
          "Significant material waste due to inaccuracies",
          "Time-consuming quality control processes",
          "Inconsistent product quality standards",
          "High labor costs for measurement operations"
        ]
      },
      solution: {
        title: "AI-Powered Precision Solution",
        description: "We developed a comprehensive AI-powered measurement system using computer vision and machine learning to deliver consistent, accurate fabric measurements with zero human intervention.",
        approach: [
          {
            phase: "Phase 1: Industry Analysis",
            description: "Deep analysis of textile manufacturing processes and measurement requirements",
            duration: "1 month"
          },
          {
            phase: "Phase 2: AI Model Development",
            description: "Development of computer vision algorithms for precise fabric measurement",
            duration: "2.5 months"
          },
          {
            phase: "Phase 3: System Integration",
            description: "Integration with existing manufacturing equipment and quality control systems",
            duration: "1 month"
          },
          {
            phase: "Phase 4: Testing & Optimization",
            description: "Comprehensive testing and system optimization for production environment",
            duration: "0.5 months"
          }
        ],
        technologies: [
          "Computer Vision",
          "AI Measurement Algorithms",
          "Quality Control Systems",
          "Textile Processing Integration",
          "Real-time Analysis",
          "Automated Reporting"
        ]
      },
      results: {
        title: "Precision Manufacturing Results",
        description: "The AI measurement system transformed textile manufacturing operations, delivering unprecedented accuracy and efficiency that revolutionized quality control processes.",
        metrics: [
          {
            metric: "Measurement Accuracy",
            before: "80%",
            after: "99.8%",
            improvement: "+19.8%"
          },
          {
            metric: "Material Waste",
            before: "15%",
            after: "2%",
            improvement: "87% reduction"
          },
          {
            metric: "Processing Speed",
            before: "50 units/hour",
            after: "200 units/hour",
            improvement: "4x faster"
          },
          {
            metric: "Quality Control Time",
            before: "30 min/batch",
            after: "5 min/batch",
            improvement: "83% reduction"
          }
        ],
        benefits: [
          "100% measurement automation achieved",
          "95% precision improvement in fabric analysis",
          "87% reduction in material waste",
          "4x increase in processing speed",
          "Consistent quality standards across all products"
        ]
      },
      testimonial: {
        quote: "The AI measurement system has completely transformed our manufacturing process. The precision and speed improvements have not only reduced our costs significantly but also improved our product quality to levels we never thought possible.",
        author: "Maria Rodriguez",
        position: "Production Manager",
        company: "Textile Manufacturing Corp"
      }
    },
    '5': {
      id: 5,
      title: "Tire Damage Detection",
      client: "Automotive Manufacturing Ltd",
      category: "Automotive Quality Control",
      image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=500&fit=crop",
      overview: "Advanced AI-powered tire quality inspection system that ensures 100% defect detection before tires are fitted to vehicles, maintaining the highest safety and quality standards in automotive manufacturing.",
      challenge: {
        title: "Automotive Safety Challenge",
        description: "Manual tire inspection processes couldn't guarantee 100% defect detection, creating potential safety risks and quality control issues. The automotive industry's stringent safety requirements demanded a more reliable solution.",
        painPoints: [
          "Manual inspection missing 5-8% of defects",
          "Potential safety risks from defective tires",
          "Slow inspection process creating bottlenecks",
          "Inconsistent quality standards across shifts",
          "High costs from recalls and warranty claims"
        ]
      },
      solution: {
        title: "AI-Powered Quality Assurance",
        description: "We developed a comprehensive AI-powered tire inspection system using advanced image processing and machine learning to detect even the smallest defects with unprecedented accuracy.",
        approach: [
          {
            phase: "Phase 1: Defect Analysis",
            description: "Comprehensive analysis of tire defect types and quality requirements",
            duration: "1 month"
          },
          {
            phase: "Phase 2: AI Model Training",
            description: "Development and training of AI models for defect detection and classification",
            duration: "3 months"
          },
          {
            phase: "Phase 3: System Integration",
            description: "Integration with production line and quality control systems",
            duration: "1.5 months"
          },
          {
            phase: "Phase 4: Validation & Deployment",
            description: "Extensive validation testing and full production deployment",
            duration: "0.5 months"
          }
        ],
        technologies: [
          "AI Image Processing",
          "Defect Detection Algorithms",
          "Quality Assurance Systems",
          "Automotive Manufacturing Integration",
          "Real-time Processing",
          "Automated Classification"
        ]
      },
      results: {
        title: "Safety Excellence Achieved",
        description: "The AI tire inspection system delivered exceptional results, ensuring the highest safety standards while dramatically improving operational efficiency.",
        metrics: [
          {
            metric: "Defect Detection",
            before: "92%",
            after: "99.8%",
            improvement: "****%"
          },
          {
            metric: "Inspection Speed",
            before: "30 tires/hour",
            after: "120 tires/hour",
            improvement: "4x faster"
          },
          {
            metric: "False Positives",
            before: "12%",
            after: "1.5%",
            improvement: "87% reduction"
          },
          {
            metric: "Quality Issues",
            before: "3%",
            after: "0.1%",
            improvement: "97% reduction"
          }
        ],
        benefits: [
          "100% quality screening implementation",
          "98.5% defect detection accuracy achieved",
          "Enhanced safety standards compliance",
          "4x increase in inspection throughput",
          "Significant reduction in warranty claims"
        ]
      },
      testimonial: {
        quote: "Mothercode's tire inspection system has set a new standard for quality control in our manufacturing process. The near-perfect defect detection rate gives us complete confidence in our product safety, and our customers have noticed the improvement in quality.",
        author: "James Wilson",
        position: "Quality Control Director",
        company: "Automotive Manufacturing Ltd"
      }
    },
    '6': {
      id: 6,
      title: "PLC & Circuit Board Detection",
      client: "Electronics Manufacturer Inc",
      category: "Electronics Manufacturing",
      image: "https://images.unsplash.com/photo-1518770660439-4636190af475?w=800&h=500&fit=crop",
      overview: "Advanced conveyor-based AI system for continuous circuit board inspection that ensures relays and circuits are correctly positioned using real-time image processing technology, enabling 24/7 quality control.",
      challenge: {
        title: "Electronics Manufacturing Challenge",
        description: "Manual circuit board inspection couldn't keep pace with high-speed production lines, creating bottlenecks and quality control issues. The complexity of modern electronics required precision inspection that human operators couldn't consistently deliver at production speeds.",
        painPoints: [
          "Manual inspection creating production bottlenecks",
          "Inconsistent quality control across different shifts",
          "High-speed production lines exceeding human inspection capacity",
          "Complex circuit patterns difficult to verify manually",
          "Quality issues discovered late in the production process"
        ]
      },
      solution: {
        title: "Intelligent Automation Solution",
        description: "We developed a sophisticated conveyor-integrated AI system that performs continuous, real-time circuit board inspection, ensuring perfect component placement and circuit integrity at production line speeds.",
        approach: [
          {
            phase: "Phase 1: Production Line Analysis",
            description: "Comprehensive analysis of manufacturing processes and quality requirements",
            duration: "1.5 months"
          },
          {
            phase: "Phase 2: AI Vision Development",
            description: "Development of advanced computer vision algorithms for circuit board analysis",
            duration: "3 months"
          },
          {
            phase: "Phase 3: Conveyor Integration",
            description: "Seamless integration with existing conveyor systems and production equipment",
            duration: "2 months"
          },
          {
            phase: "Phase 4: Testing & Optimization",
            description: "Extensive testing and optimization for 24/7 production environment",
            duration: "0.5 months"
          }
        ],
        technologies: [
          "Conveyor Integration",
          "Real-time AI Processing",
          "Circuit Analysis Algorithms",
          "Quality Control Systems",
          "High-Speed Imaging",
          "Automated Sorting"
        ]
      },
      results: {
        title: "Manufacturing Excellence Results",
        description: "The AI inspection system revolutionized electronics manufacturing, delivering continuous quality control that matches production line speeds while maintaining exceptional accuracy.",
        metrics: [
          {
            metric: "Inspection Speed",
            before: "50 boards/hour",
            after: "500 boards/hour",
            improvement: "10x faster"
          },
          {
            metric: "Detection Accuracy",
            before: "85%",
            after: "99.2%",
            improvement: "+14.2%"
          },
          {
            metric: "Production Uptime",
            before: "16 hours/day",
            after: "24 hours/day",
            improvement: "50% increase"
          },
          {
            metric: "Quality Issues",
            before: "5%",
            after: "0.3%",
            improvement: "94% reduction"
          }
        ],
        benefits: [
          "24/7 continuous monitoring capability",
          "99% inspection accuracy achieved",
          "10x increase in inspection throughput",
          "Real-time defect detection and sorting",
          "Seamless integration with production workflow"
        ]
      },
      testimonial: {
        quote: "The AI circuit board inspection system has transformed our manufacturing capabilities. We now have 24/7 quality control that exceeds human accuracy while keeping pace with our fastest production lines. It's been a game-changer for our operations.",
        author: "David Chen",
        position: "Manufacturing Engineering Manager",
        company: "Electronics Manufacturer Inc"
      }
    },
    '7': {
      id: 7,
      title: "AI Cloud Log Tracker",
      client: "Enterprise Cloud Solutions",
      category: "Cloud Security",
      image: "https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=800&h=500&fit=crop",
      overview: "Intelligent log tracking system for Azure and AWS environments with AI knowledge graph technology that enables natural language queries for enhanced cloud security monitoring and threat detection.",
      challenge: {
        title: "Cloud Security Challenge",
        description: "Enterprise cloud infrastructure generated massive volumes of complex logs that were difficult to analyze efficiently. Traditional log analysis tools couldn't provide the intelligent insights needed for proactive security monitoring and rapid threat response.",
        painPoints: [
          "Overwhelming volume of cloud logs across multiple platforms",
          "Complex query languages requiring specialized expertise",
          "Slow threat detection and response times",
          "Difficulty correlating events across different cloud services",
          "Manual analysis consuming significant security team resources"
        ]
      },
      solution: {
        title: "AI-Powered Security Intelligence",
        description: "We developed an advanced AI knowledge graph system that transforms complex cloud log analysis into intuitive natural language interactions, enabling rapid threat detection and intelligent security insights.",
        approach: [
          {
            phase: "Phase 1: Cloud Architecture Analysis",
            description: "Comprehensive analysis of Azure and AWS log structures and security requirements",
            duration: "2 months"
          },
          {
            phase: "Phase 2: AI Knowledge Graph Development",
            description: "Development of intelligent knowledge graph for log correlation and analysis",
            duration: "4 months"
          },
          {
            phase: "Phase 3: Natural Language Interface",
            description: "Implementation of natural language query processing and response system",
            duration: "2 months"
          },
          {
            phase: "Phase 4: Integration & Security Testing",
            description: "Full integration with cloud platforms and comprehensive security validation",
            duration: "1 month"
          }
        ],
        technologies: [
          "Azure Integration",
          "AWS Services",
          "AI Knowledge Graph",
          "Natural Language Processing",
          "Security Monitoring",
          "Threat Intelligence"
        ]
      },
      results: {
        title: "Security Intelligence Transformation",
        description: "The AI log tracker revolutionized cloud security operations, delivering intelligent insights and dramatically improving threat detection capabilities across the entire cloud infrastructure.",
        metrics: [
          {
            metric: "Threat Detection Speed",
            before: "4 hours",
            after: "12 minutes",
            improvement: "95% faster"
          },
          {
            metric: "Query Complexity",
            before: "Expert required",
            after: "Natural language",
            improvement: "100% accessible"
          },
          {
            metric: "Log Analysis Time",
            before: "8 hours/day",
            after: "1 hour/day",
            improvement: "87% reduction"
          },
          {
            metric: "Security Insights",
            before: "5 per week",
            after: "50 per week",
            improvement: "10x increase"
          }
        ],
        benefits: [
          "Enhanced security monitoring with AI intelligence",
          "Natural language queries eliminating technical barriers",
          "70% faster threat detection and response",
          "Intelligent correlation across cloud platforms",
          "Proactive security insights and recommendations"
        ]
      },
      testimonial: {
        quote: "Mothercode's AI log tracker has revolutionized our cloud security operations. What used to take our team hours of complex analysis can now be done in minutes with simple natural language queries. Our threat response time has improved dramatically.",
        author: "Sarah Mitchell",
        position: "Cloud Security Director",
        company: "Enterprise Cloud Solutions"
      }
    },
    '8': {
      id: 8,
      title: "AI Education Agent",
      client: "Education Institute",
      category: "Education Technology",
      image: "https://images.unsplash.com/photo-1523240795612-9a054b0db644?w=800&h=500&fit=crop",
      overview: "Intelligent agent system for educational institutions that manages admissions, handles student queries, and streamlines administrative processes using advanced AI to enhance educational service delivery.",
      challenge: {
        title: "Educational Administration Challenge",
        description: "Educational institutions faced overwhelming administrative workloads with manual admission processes and repetitive student queries consuming significant staff resources. The need for 24/7 student support and efficient admission management required an intelligent solution.",
        painPoints: [
          "Manual admission processes overwhelming staff",
          "Repetitive student queries consuming resources",
          "Limited availability for student support outside office hours",
          "Inconsistent information delivery across departments",
          "Slow response times affecting student satisfaction"
        ]
      },
      solution: {
        title: "Intelligent Educational Assistant",
        description: "We developed a comprehensive AI agent system that automates admission processes, provides intelligent responses to student queries, and streamlines institutional data management for enhanced educational service delivery.",
        approach: [
          {
            phase: "Phase 1: Educational Process Analysis",
            description: "Comprehensive analysis of admission workflows and student service requirements",
            duration: "1 month"
          },
          {
            phase: "Phase 2: AI Agent Development",
            description: "Development of intelligent agents for admission processing and query handling",
            duration: "3 months"
          },
          {
            phase: "Phase 3: System Integration",
            description: "Integration with existing educational management systems and databases",
            duration: "1.5 months"
          },
          {
            phase: "Phase 4: Training & Deployment",
            description: "Staff training and full system deployment across all departments",
            duration: "0.5 months"
          }
        ],
        technologies: [
          "AI Agents",
          "Educational Data Management",
          "Admission Processing Automation",
          "Natural Language Processing",
          "Institutional Intelligence",
          "24/7 Support Systems"
        ]
      },
      results: {
        title: "Educational Excellence Achieved",
        description: "The AI education agent transformed institutional operations, delivering exceptional efficiency improvements and significantly enhancing the student experience through intelligent automation.",
        metrics: [
          {
            metric: "Administrative Workload",
            before: "40 hours/week",
            after: "16 hours/week",
            improvement: "60% reduction"
          },
          {
            metric: "Response Time",
            before: "24 hours",
            after: "Instant",
            improvement: "100% faster"
          },
          {
            metric: "Student Satisfaction",
            before: "70%",
            after: "95%",
            improvement: "+25%"
          },
          {
            metric: "Admission Processing",
            before: "5 days",
            after: "1 day",
            improvement: "80% faster"
          }
        ],
        benefits: [
          "Streamlined admission processes with 80% time reduction",
          "24/7 automated student support and query handling",
          "Enhanced efficiency with 60% administrative workload reduction",
          "Improved student satisfaction scores by 25%",
          "Consistent information delivery across all departments"
        ]
      },
      testimonial: {
        quote: "The AI education agent has completely transformed how we serve our students. The system handles routine queries instantly, our admission process is now incredibly efficient, and our staff can focus on more meaningful student interactions. Student satisfaction has never been higher.",
        author: "Dr. Emily Thompson",
        position: "Director of Student Services",
        company: "Education Institute"
      }
    },
    '9': {
      id: 9,
      title: "Line Management System (LMS)",
      client: "Manufacturing Operations Ltd",
      category: "Factory Management",
      image: "https://images.unsplash.com/photo-1565793298595-6a879b1d9492?w=800&h=500&fit=crop",
      overview: "Revolutionary drag-and-drop roster management system that transforms factory operations through intuitive employee scheduling, real-time time tracking, break management, and comprehensive performance analytics.",
      challenge: {
        title: "Factory Management Challenge",
        description: "Manufacturing Operations Ltd struggled with inefficient manual roster management, leading to scheduling conflicts, poor time tracking, unmanaged breaks, and difficulty in monitoring employee performance and productivity across multiple production lines.",
        painPoints: [
          "Manual roster creation taking hours of administrative time",
          "Frequent scheduling conflicts and double-bookings",
          "No real-time visibility into employee locations and status",
          "Inefficient break management causing production delays",
          "Lack of performance tracking and productivity insights"
        ]
      },
      solution: {
        title: "Intelligent Factory Management Solution",
        description: "We developed a comprehensive Line Management System with intuitive drag-and-drop interface, real-time tracking capabilities, automated break management, and advanced analytics to revolutionize factory operations.",
        approach: [
          {
            phase: "Phase 1: Factory Operations Analysis",
            description: "Comprehensive study of existing roster management processes and workflow optimization opportunities",
            duration: "1 month"
          },
          {
            phase: "Phase 2: Drag-and-Drop Interface Development",
            description: "Development of intuitive visual interface for employee scheduling and line assignment",
            duration: "2.5 months"
          },
          {
            phase: "Phase 3: Time Tracking & Analytics Integration",
            description: "Implementation of real-time tracking, break management, and performance analytics systems",
            duration: "2 months"
          },
          {
            phase: "Phase 4: Testing & Factory Deployment",
            description: "Comprehensive testing with live factory operations and full system deployment",
            duration: "0.5 months"
          }
        ],
        technologies: [
          "Drag & Drop Interface",
          "Real-time Time Tracking",
          "Performance Analytics",
          "Factory Management Systems",
          "Break Management Automation",
          "Employee Productivity Monitoring"
        ]
      },
      results: {
        title: "Factory Efficiency Revolution",
        description: "The Line Management System transformed factory operations, delivering unprecedented efficiency improvements and comprehensive visibility into workforce management.",
        metrics: [
          {
            metric: "Scheduling Speed",
            before: "4 hours/day",
            after: "20 minutes/day",
            improvement: "90% faster"
          },
          {
            metric: "Scheduling Conflicts",
            before: "15 per week",
            after: "0 per week",
            improvement: "100% elimination"
          },
          {
            metric: "Factory Productivity",
            before: "100% baseline",
            after: "135% baseline",
            improvement: "35% increase"
          },
          {
            metric: "Break Management",
            before: "Manual tracking",
            after: "Automated system",
            improvement: "100% automation"
          }
        ],
        benefits: [
          "90% faster roster creation with drag-and-drop interface",
          "Real-time employee location and status tracking",
          "Automated break management eliminating production delays",
          "Comprehensive performance analytics and reporting",
          "35% improvement in overall factory productivity"
        ]
      },
      testimonial: {
        quote: "Mothercode's Line Management System has revolutionized our factory operations. The drag-and-drop interface makes scheduling incredibly intuitive, real-time tracking gives us complete visibility, and the performance analytics help us optimize productivity like never before. It's transformed how we manage our workforce.",
        author: "Michael Rodriguez",
        position: "Factory Operations Manager",
        company: "Manufacturing Operations Ltd"
      }
    }
  };

  const caseStudy = caseStudies[id || ''];

  if (!caseStudy) {
    return (
      <div className="pt-20 min-h-screen bg-gradient-to-br from-orange-50 via-white to-amber-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Case Study Not Found</h1>
          <Link to="/portfolio" className="text-orange-600 hover:text-orange-700">
            ← Back to Portfolio
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-20 min-h-screen bg-gradient-to-br from-orange-50 via-white to-amber-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Back Button */}
        <Link
          to="/portfolio"
          className="inline-flex items-center gap-2 text-orange-600 hover:text-orange-700 font-medium mb-8 transition-colors duration-200"
        >
          <ArrowLeftIcon className="w-5 h-5" />
          Back to Portfolio
        </Link>

        {/* Hero Section */}
        <div className="mb-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center gap-4 mb-6">
                <span className="px-4 py-2 bg-orange-600 text-white text-sm font-semibold rounded-full">
                  {caseStudy.category}
                </span>
              </div>
              
              <h1 className="text-4xl lg:text-5xl font-heading font-bold text-gray-900 mb-4">
                {caseStudy.title}
              </h1>
              
              <div className="flex items-center gap-3 mb-6">
                <BuildingOfficeIcon className="w-6 h-6 text-orange-600" />
                <h2 className="text-2xl font-semibold text-orange-600">
                  {caseStudy.client}
                </h2>
              </div>
              
              <p className="text-lg text-gray-600 leading-relaxed">
                {caseStudy.overview}
              </p>
            </div>
            
            <div className="relative">
              <img 
                src={caseStudy.image} 
                alt={caseStudy.title}
                className="w-full h-96 object-cover rounded-3xl shadow-xl"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-3xl"></div>
            </div>
          </div>
        </div>

        {/* Challenge Section */}
        <div className="mb-16">
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-red-200 shadow-lg">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                <BuildingOfficeIcon className="w-6 h-6 text-red-600" />
              </div>
              <h3 className="text-2xl font-heading font-bold text-gray-900">
                {caseStudy.challenge.title}
              </h3>
            </div>

            <p className="text-gray-600 leading-relaxed mb-6">
              {caseStudy.challenge.description}
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {caseStudy.challenge.painPoints.map((point: string, index: number) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-gray-700">{point}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Solution Section */}
        <div className="mb-16">
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-blue-200 shadow-lg">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <LightBulbIcon className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-2xl font-heading font-bold text-gray-900">
                {caseStudy.solution.title}
              </h3>
            </div>

            <p className="text-gray-600 leading-relaxed mb-8">
              {caseStudy.solution.description}
            </p>

            {/* Implementation Phases */}
            <div className="mb-8">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Implementation Approach</h4>
              <div className="space-y-4">
                {caseStudy.solution.approach.map((phase: any, index: number) => (
                  <div key={index} className="flex gap-4">
                    <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">
                      {index + 1}
                    </div>
                    <div>
                      <h5 className="font-semibold text-gray-900 mb-1">{phase.phase}</h5>
                      <p className="text-gray-600 text-sm mb-1">{phase.description}</p>
                      <span className="text-xs text-blue-600 font-medium">{phase.duration}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Technologies Used */}
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Technologies Used</h4>
              <div className="flex flex-wrap gap-2">
                {caseStudy.solution.technologies.map((tech: string, index: number) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full"
                  >
                    {tech}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Results Section */}
        <div className="mb-16">
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-green-200 shadow-lg">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <ChartBarIcon className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="text-2xl font-heading font-bold text-gray-900">
                {caseStudy.results.title}
              </h3>
            </div>

            <p className="text-gray-600 leading-relaxed mb-8">
              {caseStudy.results.description}
            </p>

            {/* Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {caseStudy.results.metrics.map((metric: any, index: number) => (
                <div key={index} className="bg-gradient-to-br from-green-50 to-emerald-50 p-4 rounded-xl border border-green-200">
                  <h5 className="font-semibold text-gray-900 mb-2">{metric.metric}</h5>
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Before:</span>
                      <span className="font-medium text-red-600">{metric.before}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">After:</span>
                      <span className="font-medium text-green-600">{metric.after}</span>
                    </div>
                    <div className="text-center">
                      <span className="inline-block px-2 py-1 bg-green-600 text-white text-xs font-bold rounded-full">
                        {metric.improvement}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Benefits */}
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Key Benefits Achieved</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {caseStudy.results.benefits.map((benefit: string, index: number) => (
                  <div key={index} className="flex items-start gap-3">
                    <CheckCircleIcon className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Testimonial Section */}
        <div className="mb-16">
          <div className="bg-gradient-to-r from-orange-600 to-amber-700 rounded-3xl p-8 text-white">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                <UserGroupIcon className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-2xl font-heading font-bold">Client Testimonial</h3>
            </div>

            <blockquote className="text-lg leading-relaxed mb-6 italic">
              "{caseStudy.testimonial.quote}"
            </blockquote>

            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                <span className="text-lg font-bold">{caseStudy.testimonial.author.charAt(0)}</span>
              </div>
              <div>
                <div className="font-semibold">{caseStudy.testimonial.author}</div>
                <div className="text-orange-100">{caseStudy.testimonial.position}</div>
                <div className="text-orange-200 text-sm">{caseStudy.testimonial.company}</div>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-orange-200 shadow-lg">
            <div className="flex items-center justify-center gap-3 mb-4">
              <TrophyIcon className="w-8 h-8 text-orange-600" />
              <h3 className="text-2xl font-heading font-bold text-gray-900">Ready for Your Success Story?</h3>
            </div>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Let's discuss how we can transform your business with proven solutions tailored to your specific needs.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/contact"
                className="px-8 py-3 bg-gradient-to-r from-orange-600 to-amber-700 text-white rounded-xl font-semibold hover:shadow-lg transition-all duration-300"
              >
                Start Your Project
              </Link>
              <Link
                to="/portfolio"
                className="px-8 py-3 bg-white border border-orange-300 text-orange-600 rounded-xl font-semibold hover:bg-orange-50 transition-all duration-300"
              >
                View More Projects
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CaseStudy;
