import React from 'react';
import { useNavigate } from 'react-router-dom';
import { DotLottieReact } from '@lottiefiles/dotlottie-react';

const AISpecialization: React.FC = () => {

  return (
    <section className="py-16 sm:py-20 md:py-24 bg-gradient-to-br from-gray-50 via-white to-orange-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-orange-600 to-amber-700 text-white rounded-full text-sm font-medium mb-8 shadow-lg">
            <span className="w-3 h-3 bg-white rounded-full mr-3 animate-pulse"></span>
            AI Specialization
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-heading font-extrabold text-gray-900 mb-6 leading-tight tracking-tight">
            Enterprise <span className="bg-gradient-to-r from-orange-600 to-amber-700 bg-clip-text text-transparent">AI Solutions</span>
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed max-w-4xl mx-auto font-medium">
            We specialize in advanced AI technologies including computer vision, machine learning, and intelligent automation 
            to solve complex industrial and business challenges.
          </p>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center mb-16">
          {/* Left side - Lottie Animation */}
          <div className="order-2 lg:order-1">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-orange-200 to-amber-200 rounded-3xl blur-3xl opacity-30 animate-pulse"></div>
              <div className="relative">
                <DotLottieReact
                  src="/src/animations/MachineLearning.lottie"
                  loop
                  autoplay
                  className="w-full h-auto scale-125 lg:scale-150 xl:scale-175"
                />
              </div>
              
              {/* Floating elements */}
              <div className="absolute -top-4 -right-4 w-20 h-20 bg-gradient-to-r from-orange-400 to-amber-500 rounded-full opacity-20 animate-bounce"></div>
              <div className="absolute -bottom-6 -left-6 w-16 h-16 bg-gradient-to-r from-amber-400 to-orange-500 rounded-full opacity-30 animate-pulse"></div>
            </div>
          </div>

          {/* Right side - Content Overview */}
          <div className="order-1 lg:order-2">
            <div className="space-y-8">
              <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-orange-200 shadow-lg">
                <h3 className="text-2xl sm:text-3xl font-heading font-bold text-gray-900 mb-6 tracking-tight">
                  Our AI Expertise
                </h3>
                <div className="space-y-4">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-3 h-3 bg-orange-500 rounded-full mt-2"></div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Image Processing & Computer Vision</h4>
                      <p className="text-gray-600 text-sm">Advanced object detection and image segmentation</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-3 h-3 bg-orange-500 rounded-full mt-2"></div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Industrial Automation</h4>
                      <p className="text-gray-600 text-sm">Warehouse and assembly line optimization</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-3 h-3 bg-orange-500 rounded-full mt-2"></div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Intelligent Agents</h4>
                      <p className="text-gray-600 text-sm">AI-powered automation and cloud management</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-3 h-3 bg-orange-500 rounded-full mt-2"></div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Quality Assurance</h4>
                      <p className="text-gray-600 text-sm">Automated testing and validation systems</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gradient-to-br from-orange-500 to-amber-600 rounded-2xl p-6 text-white text-center">
                  <div className="text-3xl font-bold mb-2">25+</div>
                  <div className="text-sm opacity-90">AI Projects</div>
                </div>
                <div className="bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl p-6 text-white text-center">
                  <div className="text-3xl font-bold mb-2">99%</div>
                  <div className="text-sm opacity-90">Accuracy Rate</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
      </div>
    </section>
  );
};

export default AISpecialization;
