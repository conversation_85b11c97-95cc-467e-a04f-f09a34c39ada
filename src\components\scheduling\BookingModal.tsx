import React, { useState } from 'react';
import Modal from '../ui/Modal';
import CalendarView from './CalendarView';
import TimeSlotPicker from './TimeSlotPicker';
import BookingForm from './BookingForm';
import BookingConfirmation from './BookingConfirmation';
import { CalendarIcon, ClockIcon, UserIcon, CheckCircleIcon } from '@heroicons/react/24/outline';

interface BookingModalProps {
  isOpen: boolean;
  onClose: () => void;
}

type BookingStep = 'calendar' | 'timeSlot' | 'form' | 'confirmation';

const BookingModal: React.FC<BookingModalProps> = ({ isOpen, onClose }) => {
  const [currentStep, setCurrentStep] = useState<BookingStep>('calendar');
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<any>(null);
  const [bookingData, setBookingData] = useState<any>(null);

  const handleDateSelect = (date: string) => {
    setSelectedDate(date);
    setCurrentStep('timeSlot');
  };

  const handleTimeSlotSelect = (timeSlot: any) => {
    setSelectedTimeSlot(timeSlot);
    setCurrentStep('form');
  };

  const handleFormSubmit = (formData: any) => {
    setBookingData(formData);
    setCurrentStep('confirmation');
  };

  const handleClose = () => {
    // Reset state when closing
    setCurrentStep('calendar');
    setSelectedDate(null);
    setSelectedTimeSlot(null);
    setBookingData(null);
    onClose();
  };

  const goBack = () => {
    switch (currentStep) {
      case 'timeSlot':
        setCurrentStep('calendar');
        break;
      case 'form':
        setCurrentStep('timeSlot');
        break;
      case 'confirmation':
        setCurrentStep('form');
        break;
    }
  };

  const steps = [
    { id: 'calendar', name: 'Select Date', icon: CalendarIcon },
    { id: 'timeSlot', name: 'Choose Time', icon: ClockIcon },
    { id: 'form', name: 'Your Details', icon: UserIcon },
    { id: 'confirmation', name: 'Confirmation', icon: CheckCircleIcon },
  ];

  const currentStepIndex = steps.findIndex(step => step.id === currentStep);

  const renderStepContent = () => {
    switch (currentStep) {
      case 'calendar':
        return (
          <CalendarView
            onDateSelect={handleDateSelect}
            selectedDate={selectedDate}
          />
        );
      case 'timeSlot':
        return (
          <TimeSlotPicker
            selectedDate={selectedDate!}
            onTimeSlotSelect={handleTimeSlotSelect}
            onBack={goBack}
          />
        );
      case 'form':
        return (
          <BookingForm
            selectedDate={selectedDate!}
            selectedTimeSlot={selectedTimeSlot}
            onSubmit={handleFormSubmit}
            onBack={goBack}
          />
        );
      case 'confirmation':
        return (
          <BookingConfirmation
            bookingData={bookingData}
            selectedDate={selectedDate!}
            selectedTimeSlot={selectedTimeSlot}
            onClose={handleClose}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      size="lg"
      className="max-h-[90vh] overflow-hidden"
    >
      <div className="flex flex-col h-full">
        {/* Progress Steps */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = index === currentStepIndex;
              const isCompleted = index < currentStepIndex;
              
              return (
                <div key={step.id} className="flex items-center">
                  <div className={`
                    flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300
                    ${isActive 
                      ? 'border-orange-500 bg-orange-500 text-white' 
                      : isCompleted 
                        ? 'border-green-500 bg-green-500 text-white'
                        : 'border-gray-300 bg-white text-gray-400'
                    }
                  `}>
                    <Icon className="w-5 h-5" />
                  </div>
                  <div className="ml-3 hidden sm:block">
                    <p className={`text-sm font-medium ${
                      isActive ? 'text-orange-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                    }`}>
                      {step.name}
                    </p>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`
                      hidden sm:block w-12 h-0.5 mx-4 transition-colors duration-300
                      ${isCompleted ? 'bg-green-500' : 'bg-gray-300'}
                    `} />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Step Content */}
        <div className="flex-1 overflow-y-auto">
          {renderStepContent()}
        </div>
      </div>
    </Modal>
  );
};

export default BookingModal;
