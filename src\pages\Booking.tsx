import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { BookingProvider, useBooking } from '../context/BookingContext';
import CalendarView from '../components/scheduling/CalendarView';
import TimeSlotPicker from '../components/scheduling/TimeSlotPicker';
import BookingForm from '../components/scheduling/BookingForm';
import BookingConfirmation from '../components/scheduling/BookingConfirmation';
import Button from '../components/ui/Button';

const BookingContent: React.FC = () => {
  const navigate = useNavigate();
  const { state, actions } = useBooking();

  const handleDateSelect = (date: string) => {
    actions.setSelectedDate(date);
    actions.goToStep('timeSlot');
  };

  const handleTimeSlotSelect = (timeSlot: any) => {
    actions.setSelectedTimeSlot(timeSlot);
    actions.goToStep('form');
  };

  const handleFormSubmit = async (formData: any) => {
    actions.updateFormData(formData);
    await actions.submitBooking();
  };

  const handleBack = () => {
    switch (state.step) {
      case 'timeSlot':
        actions.goToStep('calendar');
        break;
      case 'form':
        actions.goToStep('timeSlot');
        break;
      case 'confirmation':
        actions.goToStep('form');
        break;
      default:
        navigate('/');
        break;
    }
  };

  const handleClose = () => {
    actions.resetBooking();
    navigate('/');
  };

  const getStepTitle = () => {
    switch (state.step) {
      case 'calendar':
        return 'Select Date';
      case 'timeSlot':
        return 'Choose Time';
      case 'form':
        return 'Your Details';
      case 'confirmation':
        return 'Booking Confirmed';
      default:
        return 'Book Consultation';
    }
  };

  const getStepDescription = () => {
    switch (state.step) {
      case 'calendar':
        return 'Choose your preferred date for the consultation';
      case 'timeSlot':
        return 'Select an available time slot';
      case 'form':
        return 'Tell us about your project';
      case 'confirmation':
        return 'Your consultation has been scheduled';
      default:
        return '';
    }
  };

  const renderStepContent = () => {
    switch (state.step) {
      case 'calendar':
        return (
          <CalendarView
            onDateSelect={handleDateSelect}
            selectedDate={state.selectedDate}
          />
        );
      case 'timeSlot':
        return (
          <TimeSlotPicker
            selectedDate={state.selectedDate!}
            onTimeSlotSelect={handleTimeSlotSelect}
            onBack={handleBack}
          />
        );
      case 'form':
        return (
          <BookingForm
            selectedDate={state.selectedDate!}
            selectedTimeSlot={state.selectedTimeSlot!}
            onSubmit={handleFormSubmit}
            onBack={handleBack}
          />
        );
      case 'confirmation':
        return (
          <BookingConfirmation
            bookingData={state.formData as any}
            selectedDate={state.selectedDate!}
            selectedTimeSlot={state.selectedTimeSlot!}
            onClose={handleClose}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-amber-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16 sm:h-20">
            {/* Back Button */}
            <Button
              variant="ghost"
              onClick={handleBack}
              className="flex items-center space-x-2 text-gray-600 hover:text-orange-600"
            >
              <ArrowLeftIcon className="w-5 h-5" />
              <span className="hidden sm:inline">Back</span>
            </Button>

            {/* Title */}
            <div className="text-center flex-1 mx-4">
              <h1 className="text-lg sm:text-xl font-heading font-bold text-gray-900">
                {getStepTitle()}
              </h1>
              <p className="text-sm text-gray-600 hidden sm:block">
                {getStepDescription()}
              </p>
            </div>

            {/* Close Button */}
            <Button
              variant="ghost"
              onClick={handleClose}
              className="text-gray-600 hover:text-orange-600"
            >
              <span className="hidden sm:inline">Close</span>
              <span className="sm:hidden">✕</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-4">
            <div className="flex items-center justify-between max-w-md mx-auto">
              {['calendar', 'timeSlot', 'form', 'confirmation'].map((step, index) => {
                const isActive = state.step === step;
                const isCompleted = ['calendar', 'timeSlot', 'form', 'confirmation'].indexOf(state.step) > index;
                
                return (
                  <React.Fragment key={step}>
                    <div className={`
                      w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300
                      ${isActive 
                        ? 'bg-orange-500 text-white' 
                        : isCompleted 
                          ? 'bg-green-500 text-white'
                          : 'bg-gray-200 text-gray-500'
                      }
                    `}>
                      {index + 1}
                    </div>
                    {index < 3 && (
                      <div className={`
                        flex-1 h-1 mx-2 rounded transition-colors duration-300
                        ${isCompleted ? 'bg-green-500' : 'bg-gray-200'}
                      `} />
                    )}
                  </React.Fragment>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        <div className="max-w-4xl mx-auto">
          {/* Error Message */}
          {state.error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
              <p className="text-red-700 text-sm">{state.error}</p>
            </div>
          )}

          {/* Step Content */}
          <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
            {renderStepContent()}
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-white border-t border-gray-200 mt-auto">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="text-center">
            <p className="text-sm text-gray-600">
              Need help? Contact us at{' '}
              <a href="mailto:<EMAIL>" className="text-orange-600 hover:text-orange-700">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

const Booking: React.FC = () => {
  return (
    <BookingProvider>
      <BookingContent />
    </BookingProvider>
  );
};

export default Booking;
