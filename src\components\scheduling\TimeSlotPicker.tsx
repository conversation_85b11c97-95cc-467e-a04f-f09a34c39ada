import React, { useState, useEffect } from 'react';
import { ChevronLeftIcon, ClockIcon } from '@heroicons/react/24/outline';
import { formatDisplayDate, formatDisplayTime } from '../../utils/dateUtils';
import { bookingService } from '../../services/bookingService';
import { TimeSlot } from '../../types/booking';
import LoadingSpinner from '../ui/LoadingSpinner';
import Button from '../ui/Button';

interface TimeSlotPickerProps {
  selectedDate: string;
  onTimeSlotSelect: (timeSlot: TimeSlot) => void;
  onBack: () => void;
}

const TimeSlotPicker: React.FC<TimeSlotPickerProps> = ({
  selectedDate,
  onTimeSlotSelect,
  onBack
}) => {
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedSlot, setSelectedSlot] = useState<TimeSlot | null>(null);

  useEffect(() => {
    loadTimeSlots();
  }, [selectedDate]);

  const loadTimeSlots = async () => {
    setIsLoading(true);
    try {
      const response = await bookingService.getAvailableSlotsForDate(selectedDate);
      if (response.success && response.data && response.data.length > 0) {
        setTimeSlots(response.data[0].timeSlots);
      } else {
        setTimeSlots([]);
      }
    } catch (error) {
      console.error('Failed to load time slots:', error);
      setTimeSlots([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSlotSelect = (slot: TimeSlot) => {
    if (!slot.isAvailable || slot.isBooked) return;
    setSelectedSlot(slot);
  };

  const handleContinue = () => {
    if (selectedSlot) {
      onTimeSlotSelect(selectedSlot);
    }
  };

  const getSlotClasses = (slot: TimeSlot) => {
    let classes = 'p-4 rounded-xl border-2 transition-all duration-200 cursor-pointer text-center ';
    
    if (!slot.isAvailable || slot.isBooked) {
      classes += 'border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed ';
    } else if (selectedSlot?.id === slot.id) {
      classes += 'border-orange-500 bg-orange-50 text-orange-700 shadow-md ';
    } else {
      classes += 'border-gray-200 bg-white text-gray-700 hover:border-orange-300 hover:bg-orange-50 hover:shadow-md ';
    }
    
    return classes;
  };

  const availableSlots = timeSlots.filter(slot => slot.isAvailable && !slot.isBooked);
  const date = new Date(selectedDate);

  return (
    <div className="p-4 sm:p-6 lg:p-8">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="flex items-center mb-6">
          <button
            onClick={onBack}
            className="p-2 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-xl transition-colors duration-200 mr-3"
          >
            <ChevronLeftIcon className="w-5 h-5" />
          </button>
          <div>
            <h2 className="text-xl font-heading font-bold text-gray-900">
              Select Time
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              {formatDisplayDate(date)}
            </p>
          </div>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        )}

        {/* No Slots Available */}
        {!isLoading && timeSlots.length === 0 && (
          <div className="text-center py-12">
            <ClockIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No slots available
            </h3>
            <p className="text-gray-600">
              Please select a different date or contact us directly.
            </p>
          </div>
        )}

        {/* Time Slots Grid */}
        {!isLoading && timeSlots.length > 0 && (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mb-6">
              {timeSlots.map((slot) => (
                <button
                  key={slot.id}
                  onClick={() => handleSlotSelect(slot)}
                  className={getSlotClasses(slot)}
                  disabled={!slot.isAvailable || slot.isBooked}
                >
                  <div className="flex items-center justify-center space-x-2">
                    <ClockIcon className="w-4 h-4" />
                    <span className="font-medium">
                      {formatDisplayTime(slot.startTime)}
                    </span>
                  </div>
                  <div className="text-xs mt-1">
                    {slot.isBooked ? 'Booked' : slot.isAvailable ? 'Available' : 'Unavailable'}
                  </div>
                </button>
              ))}
            </div>

            {/* Summary */}
            {availableSlots.length > 0 && (
              <div className="bg-orange-50 rounded-xl p-4 mb-6">
                <div className="flex items-center space-x-2 text-orange-700">
                  <ClockIcon className="w-5 h-5" />
                  <span className="font-medium">
                    {availableSlots.length} slot{availableSlots.length !== 1 ? 's' : ''} available
                  </span>
                </div>
                <p className="text-sm text-orange-600 mt-1">
                  Each consultation is 1 hour long
                </p>
              </div>
            )}

            {/* Selected Slot Info */}
            {selectedSlot && (
              <div className="bg-white border-2 border-orange-200 rounded-xl p-4 mb-6">
                <h4 className="font-semibold text-gray-900 mb-2">Selected Time</h4>
                <div className="flex items-center space-x-2 text-orange-700">
                  <ClockIcon className="w-5 h-5" />
                  <span className="font-medium">
                    {formatDisplayTime(selectedSlot.startTime)} - {formatDisplayTime(selectedSlot.endTime)}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  {formatDisplayDate(date)}
                </p>
              </div>
            )}

            {/* Continue Button */}
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={onBack}
                className="flex-1"
              >
                Back
              </Button>
              <Button
                variant="primary"
                onClick={handleContinue}
                disabled={!selectedSlot}
                className="flex-1"
              >
                Continue
              </Button>
            </div>
          </>
        )}

        {/* Instructions */}
        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            All times are shown in your local timezone
          </p>
        </div>
      </div>
    </div>
  );
};

export default TimeSlotPicker;
