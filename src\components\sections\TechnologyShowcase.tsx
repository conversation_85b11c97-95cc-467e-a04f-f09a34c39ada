import React from 'react';
import {
  SiReact,
  SiNodedotjs,
  SiPython,
  SiTensorflow,
  SiJavascript,
  SiTypescript,
  SiPhp,
  SiRuby,
  SiGo,
  SiRust,
  SiSwift,
  SiKotlin,
  SiFlutter,
  SiVuedotjs,
  SiAngular,
  SiNextdotjs,
  SiExpress,
  SiDjango,
  SiSpring,
  SiLaravel,
  SiMongodb,
  SiPostgresql,
  SiMysql,
  SiRedis,
  SiDocker,
  SiKubernetes,
  SiGooglecloud,
  SiGraphql,
  SiFirebase,
  SiVercel,
  SiNetlify,
  SiHeroku,
  SiGitlab,
  SiGithub,
  SiPostman,
  SiVite,
  SiWebpack,
  SiJest,
  SiTailwindcss,
  SiBootstrap,
  SiSass,
  SiElectron,
  SiUnity,
  SiJupyter,
  SiNginx,
  SiLinux,
  SiHostinger,
  SiHuggingface,
  SiOpenai,
  SiAnthropic,
  SiGooglegemini,
  SiClaude,
  SiOpencv,
  SiMlflow,
  SiPytorch,
  SiScikitlearn,
  SiLangchain,
  SiOllama,
  SiPandas,
  SiNumpy,
  SiStreamlit,
  SiFastapi,
  SiMilvus,
  SiNeo4J,
  SiSqlalchemy,
  SiFlask,
  SiRedux,
  SiUbuntu,
  SiAndroidstudio,
  SiXcode,
  SiIntellijidea,
  SiPycharm,
  SiCplusplus,
  SiC,
  SiTypeorm
} from 'react-icons/si';
import { TbBrandCSharp } from "react-icons/tb";
import { FaJava } from "react-icons/fa";

const TechnologyShowcase: React.FC = () => {
  const technologies = [
    // Programming Languages
    { icon: <SiJavascript />, name: 'JavaScript', color: 'text-yellow-400' },
    { icon: <SiTypescript />, name: 'TypeScript', color: 'text-blue-500' },
    { icon: <SiPython />, name: 'Python', color: 'text-yellow-500' },
    { icon: <FaJava />, name: 'Java', color: 'text-red-500' },
    { icon: <TbBrandCSharp />, name: 'C#', color: 'text-purple-500' },
    { icon: <SiPhp />, name: 'PHP', color: 'text-indigo-500' },
    { icon: <SiRuby />, name: 'Ruby', color: 'text-red-600' },
    { icon: <SiGo />, name: 'Go', color: 'text-cyan-600' },
    { icon: <SiRust />, name: 'Rust', color: 'text-orange-600' },
    { icon: <SiSwift />, name: 'Swift', color: 'text-orange-500' },
    { icon: <SiKotlin />, name: 'Kotlin', color: 'text-purple-600' },
    { icon: <SiCplusplus />, name: 'C++', color: 'text-blue-600' },
    { icon: <SiC />, name: 'C', color: 'text-blue-400' },

    // Frontend Frameworks & Libraries
    { icon: <SiReact />, name: 'React', color: 'text-cyan-500' },
    { icon: <SiVuedotjs />, name: 'Vue.js', color: 'text-green-400' },
    { icon: <SiAngular />, name: 'Angular', color: 'text-red-500' },
    { icon: <SiNextdotjs />, name: 'Next.js', color: 'text-gray-800' },
    { icon: <SiFlutter />, name: 'Flutter', color: 'text-blue-400' },
    { icon: <SiTailwindcss />, name: 'Tailwind CSS', color: 'text-cyan-400' },
    { icon: <SiBootstrap />, name: 'Bootstrap', color: 'text-purple-600' },
    { icon: <SiSass />, name: 'Sass', color: 'text-pink-500' },
    { icon: <SiRedux />, name: 'Redux', color: 'text-purple-500' },

    // Backend Frameworks
    { icon: <SiNodedotjs />, name: 'Node.js', color: 'text-green-500' },
    { icon: <SiExpress />, name: 'Express', color: 'text-gray-600' },
    { icon: <SiDjango />, name: 'Django', color: 'text-green-600' },
    { icon: <SiFlask />, name: 'Flask', color: 'text-gray-800' },
    { icon: <SiSpring />, name: 'Spring', color: 'text-green-500' },
    { icon: <SiLaravel />, name: 'Laravel', color: 'text-red-500' },
    { icon: <SiSqlalchemy />, name: 'SQLAlchemy', color: 'text-red-600' },

    // Databases
    { icon: <SiMongodb />, name: 'MongoDB', color: 'text-green-500' },
    { icon: <SiPostgresql />, name: 'PostgreSQL', color: 'text-blue-600' },
    { icon: <SiMysql />, name: 'MySQL', color: 'text-blue-500' },
    { icon: <SiRedis />, name: 'Redis', color: 'text-red-500' },
    { icon: <SiMilvus />, name: 'Milvus', color: 'text-blue-600' },
    { icon: <SiNeo4J />, name: 'Neo4j', color: 'text-cyan-600' },
    { icon: <SiTypeorm />, name: 'TypeORM', color: 'text-red-600' },

    // Cloud & Hosting
    { icon: <SiGooglecloud />, name: 'Google Cloud', color: 'text-blue-600' },
    { icon: <SiFirebase />, name: 'Firebase', color: 'text-orange-500' },
    { icon: <SiVercel />, name: 'Vercel', color: 'text-gray-800' },
    { icon: <SiNetlify />, name: 'Netlify', color: 'text-teal-500' },
    { icon: <SiHeroku />, name: 'Heroku', color: 'text-purple-600' },
    { icon: <SiHostinger />, name: 'Hostinger', color: 'text-purple-600' },

    // DevOps & Tools
    { icon: <SiDocker />, name: 'Docker', color: 'text-blue-400' },
    { icon: <SiKubernetes />, name: 'Kubernetes', color: 'text-blue-700' },
    { icon: <SiGithub />, name: 'GitHub', color: 'text-gray-800' },
    { icon: <SiGitlab />, name: 'GitLab', color: 'text-orange-600' },
    { icon: <SiPostman />, name: 'Postman', color: 'text-orange-500' },
    { icon: <SiNginx />, name: 'Nginx', color: 'text-green-600' },
    { icon: <SiLinux />, name: 'Linux', color: 'text-yellow-600' },
    { icon: <SiUbuntu />, name: 'Ubuntu', color: 'text-orange-600' },

    // IDEs & Editors
    { icon: <SiAndroidstudio />, name: 'Android Studio', color: 'text-green-600' },
    { icon: <SiXcode />, name: 'Xcode', color: 'text-blue-500' },
    { icon: <SiIntellijidea />, name: 'IntelliJ IDEA', color: 'text-red-500' },
    { icon: <SiPycharm />, name: 'PyCharm', color: 'text-green-500' },

    // Build Tools & Testing
    { icon: <SiVite />, name: 'Vite', color: 'text-purple-500' },
    { icon: <SiWebpack />, name: 'Webpack', color: 'text-blue-500' },
    { icon: <SiJest />, name: 'Jest', color: 'text-red-600' },

    // AI & ML Frameworks
    { icon: <SiTensorflow />, name: 'TensorFlow', color: 'text-orange-500' },
    { icon: <SiJupyter />, name: 'Jupyter', color: 'text-orange-600' },
    { icon: <SiPytorch />, name: 'PyTorch', color: 'text-red-500' },
    { icon: <SiScikitlearn />, name: 'Scikit-learn', color: 'text-blue-600' },
    { icon: <SiPandas />, name: 'Pandas', color: 'text-purple-600' },
    { icon: <SiNumpy />, name: 'NumPy', color: 'text-blue-500' },
    { icon: <SiOpencv />, name: 'OpenCV', color: 'text-green-600' },
    { icon: <SiLangchain />, name: 'LangChain', color: 'text-indigo-600' },
    { icon: <SiHuggingface />, name: 'Hugging Face', color: 'text-yellow-500' },
    { icon: <SiOpenai />, name: 'OpenAI API', color: 'text-green-500' },
    { icon: <SiAnthropic />, name: 'Anthropic', color: 'text-purple-500' },
    { icon: <SiMlflow />, name: 'MLflow', color: 'text-blue-400' },
    { icon: <SiStreamlit />, name: 'Streamlit', color: 'text-red-500' },
    { icon: <SiFastapi />, name: 'FastAPI', color: 'text-teal-600' },
    { icon: <SiOllama />, name: 'Ollama', color: 'text-gray-700' },
    { icon: <SiGooglegemini />, name: 'Gemini API', color: 'text-blue-600' },
    { icon: <SiClaude />, name: 'Claude API', color: 'text-orange-600' },

    // Development Tools
    { icon: <SiElectron />, name: 'Electron', color: 'text-cyan-600' },
    { icon: <SiUnity />, name: 'Unity', color: 'text-gray-800' },
    { icon: <SiGraphql />, name: 'GraphQL', color: 'text-pink-500' }
  ];

  // Split technologies into 3 rows
  const itemsPerRow = Math.ceil(technologies.length / 3);
  const row1 = technologies.slice(0, itemsPerRow);
  const row2 = technologies.slice(itemsPerRow, itemsPerRow * 2);
  const row3 = technologies.slice(itemsPerRow * 2);

  return (
    <section className="py-16 sm:py-20 md:py-24 bg-gradient-to-br from-orange-50 via-white to-amber-50 overflow-hidden">
      <div className="w-full">
        <div className="relative overflow-hidden bg-transparent">
          {/* Header */}
          <div className="relative text-center mb-16">
            <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-orange-600 to-amber-700 text-white rounded-full text-sm font-medium mb-8 shadow-lg">
              <span className="w-3 h-3 bg-white rounded-full mr-3 animate-pulse"></span>
              25+ Years of Technology Excellence
            </div>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-heading font-extrabold text-gray-900 mb-6 leading-tight tracking-tight">
              Powered by <span className="bg-gradient-to-r from-orange-600 to-amber-700 bg-clip-text text-transparent">Modern</span> Tech Stack
            </h2>
            <div className="max-w-4xl mx-auto mb-8">
              <p className="text-xl text-gray-600 leading-relaxed mb-6 font-medium">
                At Mothercode, we leverage cutting-edge technologies to build scalable, AI-driven applications. 
                Our expertise spans across modern frameworks, cloud platforms, and emerging AI technologies.
              </p>
            </div>
          </div>

          {/* Sliding Technology Rows */}
          <div className="relative space-y-8">
            {/* Row 1 - Left to Right */}
            <div className="tech-scroll-container flex overflow-hidden">
              <div className="flex space-x-6 animate-scroll-left">
                {[...row1, ...row1].map((tech, index) => (
                  <div
                    key={`row1-${index}`}
                    className="flex-shrink-0 group tech-card"
                  >
                    <div className="flex items-center space-x-3 bg-white/80 backdrop-blur-sm border border-orange-200 rounded-2xl px-6 py-4 hover:bg-white hover:shadow-lg transition-all duration-300 hover:border-orange-400 hover:shadow-orange-100">
                      <div className={`text-2xl transition-transform duration-300 group-hover:scale-110 ${tech.color}`}>
                        {tech.icon}
                      </div>
                      <span className="text-gray-800 font-medium text-sm whitespace-nowrap">{tech.name}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Row 2 - Right to Left */}
            <div className="tech-scroll-container flex overflow-hidden">
              <div className="flex space-x-6 animate-scroll-right">
                {[...row2, ...row2].map((tech, index) => (
                  <div
                    key={`row2-${index}`}
                    className="flex-shrink-0 group tech-card"
                  >
                    <div className="flex items-center space-x-3 bg-white/80 backdrop-blur-sm border border-orange-200 rounded-2xl px-6 py-4 hover:bg-white hover:shadow-lg transition-all duration-300 hover:border-orange-400 hover:shadow-orange-100">
                      <div className={`text-2xl transition-transform duration-300 group-hover:scale-110 ${tech.color}`}>
                        {tech.icon}
                      </div>
                      <span className="text-gray-800 font-medium text-sm whitespace-nowrap">{tech.name}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Row 3 - Left to Right (Slower) */}
            <div className="tech-scroll-container flex overflow-hidden">
              <div className="flex space-x-6 animate-scroll-left-slow">
                {[...row3, ...row3].map((tech, index) => (
                  <div
                    key={`row3-${index}`}
                    className="flex-shrink-0 group tech-card"
                  >
                    <div className="flex items-center space-x-3 bg-white/80 backdrop-blur-sm border border-orange-200 rounded-2xl px-6 py-4 hover:bg-white hover:shadow-lg transition-all duration-300 hover:border-orange-400 hover:shadow-orange-100">
                      <div className={`text-2xl transition-transform duration-300 group-hover:scale-110 ${tech.color}`}>
                        {tech.icon}
                      </div>
                      <span className="text-gray-800 font-medium text-sm whitespace-nowrap">{tech.name}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TechnologyShowcase;
