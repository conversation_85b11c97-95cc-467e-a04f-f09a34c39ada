// Booking service for API interactions
import { 
  Booking, 
  BookingFormData, 
  AvailableSlot, 
  BookingApiResponse, 
  AvailableSlotsApiResponse,
  TimeSlot 
} from '../types/booking';

// Generate mock data with current and future dates
const generateMockSlots = (): AvailableSlot[] => {
  const slots: AvailableSlot[] = [];
  const today = new Date();

  // Generate slots for the next 30 days
  for (let i = 1; i <= 30; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);

    // Skip weekends
    if (date.getDay() === 0 || date.getDay() === 6) continue;

    const dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD format

    slots.push({
      date: dateStr,
      timeSlots: [
        { id: `${dateStr}-1`, startTime: '09:00', endTime: '10:00', isAvailable: true, isBooked: false },
        { id: `${dateStr}-2`, startTime: '10:30', endTime: '11:30', isAvailable: true, isBooked: false },
        { id: `${dateStr}-3`, startTime: '14:00', endTime: '15:00', isAvailable: true, isBooked: false },
        { id: `${dateStr}-4`, startTime: '15:30', endTime: '16:30', isAvailable: Math.random() > 0.3, isBooked: Math.random() < 0.2 },
      ]
    });
  }

  return slots;
};

const MOCK_AVAILABLE_SLOTS: AvailableSlot[] = generateMockSlots();

class BookingService {
  private baseUrl: string;

  constructor() {
    // Replace with your actual API base URL
    this.baseUrl = import.meta.env.REACT_APP_API_URL || 'http://localhost:3001/api';
  }

  // Get available time slots for a specific month
  async getAvailableSlots(month: string): Promise<AvailableSlotsApiResponse> {
    try {
      // TODO: Replace with actual API call
      // const response = await fetch(`${this.baseUrl}/booking/available-slots?month=${month}`);
      // const data = await response.json();
      
      // Mock response for development
      await this.delay(1000); // Simulate API delay
      
      return {
        success: true,
        data: MOCK_AVAILABLE_SLOTS,
        message: 'Available slots retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to fetch available slots',
        message: 'Please try again later'
      };
    }
  }

  // Get available slots for a specific date
  async getAvailableSlotsForDate(date: string): Promise<AvailableSlotsApiResponse> {
    try {
      // TODO: Replace with actual API call
      // const response = await fetch(`${this.baseUrl}/booking/available-slots/${date}`);
      // const data = await response.json();
      
      // Mock response for development
      await this.delay(500);
      
      const daySlots = MOCK_AVAILABLE_SLOTS.find(slot => slot.date === date);
      
      return {
        success: true,
        data: daySlots ? [daySlots] : [],
        message: 'Available slots retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to fetch available slots for date',
        message: 'Please try again later'
      };
    }
  }

  // Create a new booking
  async createBooking(bookingData: BookingFormData & { 
    timeSlotId: string;
    scheduledDate: string;
    scheduledTime: string;
  }): Promise<BookingApiResponse> {
    try {
      // TODO: Replace with actual API call
      // const response = await fetch(`${this.baseUrl}/booking`, {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify(bookingData),
      // });
      // const data = await response.json();
      
      // Mock response for development
      await this.delay(1500);
      
      const mockBooking: Booking = {
        id: `booking_${Date.now()}`,
        name: bookingData.name,
        email: bookingData.email,
        phone: bookingData.phone,
        company: bookingData.company,
        projectType: bookingData.projectType,
        message: bookingData.message,
        scheduledDate: bookingData.scheduledDate,
        scheduledTime: bookingData.scheduledTime,
        duration: 60, // 1 hour default
        status: 'pending',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      return {
        success: true,
        data: mockBooking,
        message: 'Booking created successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to create booking',
        message: 'Please try again later'
      };
    }
  }

  // Get booking by ID
  async getBooking(bookingId: string): Promise<BookingApiResponse> {
    try {
      // TODO: Replace with actual API call
      // const response = await fetch(`${this.baseUrl}/booking/${bookingId}`);
      // const data = await response.json();
      
      await this.delay(500);
      
      return {
        success: false,
        error: 'Booking not found',
        message: 'The requested booking could not be found'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to fetch booking',
        message: 'Please try again later'
      };
    }
  }

  // Cancel a booking
  async cancelBooking(bookingId: string): Promise<BookingApiResponse> {
    try {
      // TODO: Replace with actual API call
      // const response = await fetch(`${this.baseUrl}/booking/${bookingId}/cancel`, {
      //   method: 'PATCH',
      // });
      // const data = await response.json();
      
      await this.delay(1000);
      
      return {
        success: true,
        message: 'Booking cancelled successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to cancel booking',
        message: 'Please try again later'
      };
    }
  }

  // Utility method to simulate API delay
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Get business configuration
  async getBusinessConfig() {
    return {
      businessHours: {
        start: '09:00',
        end: '17:00'
      },
      workingDays: [1, 2, 3, 4, 5], // Monday to Friday
      slotDuration: 60, // 1 hour
      bufferTime: 30, // 30 minutes between slots
      maxAdvanceBooking: 30, // 30 days
      minAdvanceBooking: 2, // 2 hours
      timeZone: 'America/Los_Angeles'
    };
  }
}

// Export singleton instance
export const bookingService = new BookingService();
export default bookingService;
