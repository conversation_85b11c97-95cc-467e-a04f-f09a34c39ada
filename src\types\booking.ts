// Booking system type definitions

export interface TimeSlot {
  id: string;
  startTime: string; // ISO string
  endTime: string;   // ISO string
  isAvailable: boolean;
  isBooked: boolean;
}

export interface BookingFormData {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  projectType: string;
  message: string;
  preferredDate: string;
  preferredTime: string;
}

export interface Booking {
  id: string;
  userId?: string;
  name: string;
  email: string;
  phone?: string;
  company?: string;
  projectType: string;
  message: string;
  scheduledDate: string; // ISO string
  scheduledTime: string; // ISO string
  duration: number; // minutes
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  createdAt: string;
  updatedAt: string;
}

export interface AvailableSlot {
  date: string; // YYYY-MM-DD format
  timeSlots: TimeSlot[];
}

export interface BookingState {
  selectedDate: string | null;
  selectedTimeSlot: TimeSlot | null;
  formData: Partial<BookingFormData>;
  availableSlots: AvailableSlot[];
  isLoading: boolean;
  error: string | null;
  step: 'calendar' | 'timeSlot' | 'form' | 'confirmation';
}

export interface BookingContextType {
  state: BookingState;
  actions: {
    setSelectedDate: (date: string) => void;
    setSelectedTimeSlot: (slot: TimeSlot) => void;
    updateFormData: (data: Partial<BookingFormData>) => void;
    submitBooking: () => Promise<void>;
    resetBooking: () => void;
    goToStep: (step: BookingState['step']) => void;
    fetchAvailableSlots: (month: string) => Promise<void>;
  };
}

export interface CalendarDay {
  date: string;
  day: number;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
  hasAvailableSlots: boolean;
  isDisabled: boolean;
}

export interface ProjectType {
  id: string;
  name: string;
  description: string;
  estimatedDuration: number; // minutes
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface BookingApiResponse extends ApiResponse<Booking> {}
export interface AvailableSlotsApiResponse extends ApiResponse<AvailableSlot[]> {}

// Configuration
export interface BookingConfig {
  businessHours: {
    start: string; // "09:00"
    end: string;   // "17:00"
  };
  workingDays: number[]; // [1, 2, 3, 4, 5] for Mon-Fri
  slotDuration: number; // minutes
  bufferTime: number; // minutes between slots
  maxAdvanceBooking: number; // days
  minAdvanceBooking: number; // hours
  timeZone: string;
}
