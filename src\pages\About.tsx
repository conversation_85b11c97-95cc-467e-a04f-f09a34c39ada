import React from 'react';
import {
  TrophyIcon,
  GlobeAltIcon,
  AcademicCapIcon,
  CogIcon,
  EyeIcon,
  BeakerIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline';

const About: React.FC = () => {
  const timelineEvents = [
    {
      year: "2025",
      title: "AI Dimension Predictor and Log Tracker Cloud AI",
      description: "Advanced AI system for dimensional prediction and intelligent cloud log tracking with natural language processing capabilities.",
      icon: <BeakerIcon className="w-6 h-6" />,
      category: "AI Innovation"
    },
    {
      year: "2024",
      title: "Line Management System (LMS)",
      description: "Revolutionary drag-and-drop roster management system for factory operations with real-time tracking and performance analytics.",
      icon: <CogIcon className="w-6 h-6" />,
      category: "Factory Management"
    },
    {
      year: "2023",
      title: "Launch of DeskGee",
      description: "The next-generation platform revolutionizing customer experience.",
      icon: <GlobeAltIcon className="w-6 h-6" />,
      category: "Product Launch"
    },
    {
      year: "2021",
      title: "Establishment of ZiliconCloud USA",
      description: "Expanded our global footprint by establishing operations in the USA.",
      icon: <BuildingOfficeIcon className="w-6 h-6" />,
      category: "Global Expansion"
    },
    {
      year: "2019",
      title: "AI-Based Quality Control in Car Manufacturing",
      description: "Implemented AI technology to enhance precision and efficiency in car manufacturing processes.",
      icon: <CogIcon className="w-6 h-6" />,
      category: "AI Innovation"
    },
    {
      year: "2016",
      title: "Introduction of LaserPicking",
      description: "Innovated inventory management with the cutting-edge LaserPicking technology.",
      icon: <EyeIcon className="w-6 h-6" />,
      category: "Technology Innovation"
    },
    {
      year: "2015",
      title: "Development of the RFID Digital Picking System",
      description: "Streamlined supply chain processes with our RFID Digital Picking System.",
      icon: <CogIcon className="w-6 h-6" />,
      category: "Supply Chain Innovation"
    },
    {
      year: "2013",
      title: "Joint Patent with The Jikei University School of Medicine",
      description: "Collaborated on a joint patent for image processing applications in the medical field.",
      icon: <AcademicCapIcon className="w-6 h-6" />,
      category: "Medical Innovation"
    },
    {
      year: "2013",
      title: "Feature in Drift Meeting by YOKOMO",
      description: "Our technology was featured in the special round of the drift meeting hosted by YOKOMO.",
      icon: <TrophyIcon className="w-6 h-6" />,
      category: "Recognition"
    },
    {
      year: "2012",
      title: "Advancement in Supply Chain Automation",
      description: "Enhanced supply chain automation with image processing and laser picking technology.",
      icon: <CogIcon className="w-6 h-6" />,
      category: "Automation"
    },
    {
      year: "2012",
      title: "Showcase at CeMAT India",
      description: "Demonstrated Laser Picking technology at CeMAT India in Bangalore.",
      icon: <GlobeAltIcon className="w-6 h-6" />,
      category: "Industry Showcase"
    },
    {
      year: "2012",
      title: "Trademark and Patent Acquisition",
      description: "Acquired trademark and patent for Laser Picking technology.",
      icon: <TrophyIcon className="w-6 h-6" />,
      category: "Intellectual Property"
    },
    {
      year: "2011",
      title: "Image Processing Development",
      description: "Initiated development in image processing using pattern matching, edge detection, and color component recognition to meet industrial demands.",
      icon: <EyeIcon className="w-6 h-6" />,
      category: "Technology Development"
    },
    {
      year: "2010",
      title: "3D Object Recognition R&D",
      description: "Began research and development in 3D object recognition (position recognition) with support from the UFJ Technology Development Foundation.",
      icon: <BeakerIcon className="w-6 h-6" />,
      category: "Research & Development"
    },
    {
      year: "2009",
      title: "Medical Image Processing R&D",
      description: "Collaborated with Jikei University School of Medicine on research and development in image processing with OpenCV for medical image analysis.",
      icon: <AcademicCapIcon className="w-6 h-6" />,
      category: "Medical Research"
    },
    {
      year: "2003",
      title: "Soccer Analytics Project",
      description: "Launched a joint project with Jubilo Iwata and Tsinghua University on an object tracking system for soccer analysis, providing post-game insights and training enhancements.",
      icon: <BeakerIcon className="w-6 h-6" />,
      category: "Sports Analytics"
    },
    {
      year: "2002",
      title: "Partnership with Inspeedia, Japan",
      description: "Formed a strategic partnership with Inspeedia, Japan.",
      icon: <BuildingOfficeIcon className="w-6 h-6" />,
      category: "Strategic Partnership"
    },
    {
      year: "2001",
      title: "Founding of MSS India",
      description: "Marked the beginning of our journey with the founding of MSS India.",
      icon: <TrophyIcon className="w-6 h-6" />,
      category: "Company Foundation"
    }
  ];

  return (
    <div className="pt-10 min-h-screen bg-gradient-to-br from-orange-50 via-white to-amber-50">

      {/* Timeline Section */}
      <div className="relative overflow-hidden bg-white/50 backdrop-blur-sm">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-gradient-to-r from-orange-500/10 via-amber-500/10 to-red-500/10"></div>
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,rgba(251,146,60,0.05),transparent_50%)]"></div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-20 relative">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-heading font-bold text-gray-900 mb-6">
              Our <span className="bg-gradient-to-r from-orange-600 to-amber-700 bg-clip-text text-transparent">Journey</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              From a startup vision in 2001 to a global technology leader in {new Date().getFullYear()} - witness our remarkable journey
              of innovation, breakthroughs, and industry-defining achievements
            </p>
          </div>

          {/* Attractive Roadmap Design */}
          <div className="relative max-w-7xl mx-auto">
            {/* Flowing Timeline Path */}
            <div className="hidden lg:block absolute inset-0 pointer-events-none">
              <svg className="w-full h-full" viewBox="0 0 1200 800" fill="none">
                <path
                  d="M100 400 Q300 200 500 400 T900 400 Q1000 300 1100 400"
                  stroke="url(#gradient)"
                  strokeWidth="3"
                  fill="none"
                  className="animate-pulse"
                />
                <defs>
                  <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#f97316" />
                    <stop offset="50%" stopColor="#f59e0b" />
                    <stop offset="100%" stopColor="#ef4444" />
                  </linearGradient>
                </defs>
              </svg>
            </div>

            {/* Timeline Events in Flowing Layout */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 relative z-10">
              {timelineEvents.map((event, index) => (
                <div
                  key={index}
                  className={`group relative ${index % 2 === 0 ? 'lg:mt-0' : 'lg:mt-16'}`}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  {/* Timeline Card */}
                  <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl p-6 border border-orange-200 hover:border-orange-400 transition-all duration-500 hover:scale-105 hover:shadow-xl hover:shadow-orange-500/25">

                    {/* Floating Year */}
                    <div className="absolute -top-6 left-1/2 transform -translate-x-1/2">
                      <div className="bg-gradient-to-r from-orange-500 via-amber-500 to-red-500 text-white px-6 py-2 rounded-full text-lg font-bold shadow-xl animate-bounce">
                        {event.year}
                      </div>
                    </div>

                    {/* Glowing Icon */}
                    <div className="flex justify-center mb-6 mt-4">
                      <div className="relative">
                        <div className="absolute inset-0 bg-gradient-to-r from-orange-500 to-amber-500 rounded-2xl blur-lg opacity-75 group-hover:opacity-100 transition-opacity duration-500"></div>
                        <div className="relative w-16 h-16 bg-gradient-to-r from-orange-500 to-amber-500 rounded-2xl flex items-center justify-center group-hover:rotate-12 transition-transform duration-500">
                          <div className="text-white text-xl">
                            {event.icon}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Category Tag */}
                    <div className="text-center mb-4">
                      <span className="inline-block px-4 py-2 bg-orange-100 border border-orange-200 text-orange-800 text-xs font-semibold rounded-full">
                        {event.category}
                      </span>
                    </div>

                    {/* Content */}
                    <div className="text-center">
                      <h3 className="text-lg font-heading font-bold text-gray-900 mb-3 group-hover:text-orange-600 transition-colors duration-300">
                        {event.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed text-sm group-hover:text-gray-700 transition-colors duration-300">
                        {event.description}
                      </p>
                    </div>

                    {/* Hover Background */}
                    <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-orange-50 to-amber-50 opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10"></div>

                    {/* Subtle Effects */}
                    <div className="absolute inset-0 rounded-3xl overflow-hidden">
                      <div className="absolute top-2 left-2 w-1 h-1 bg-orange-400 rounded-full opacity-0 group-hover:opacity-60 group-hover:animate-pulse"></div>
                      <div className="absolute top-4 right-4 w-1 h-1 bg-amber-400 rounded-full opacity-0 group-hover:opacity-60 group-hover:animate-pulse"></div>
                      <div className="absolute bottom-4 left-4 w-1 h-1 bg-orange-500 rounded-full opacity-0 group-hover:opacity-60 group-hover:animate-pulse"></div>
                    </div>
                  </div>

                  {/* Connection Line for Mobile */}
                  {index < timelineEvents.length - 1 && (
                    <div className="lg:hidden flex justify-center mt-8 mb-8">
                      <div className="w-1 h-8 bg-gradient-to-b from-orange-500 to-amber-500 rounded-full"></div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Floating Elements */}
            <div className="absolute top-10 left-10 w-20 h-20 bg-gradient-to-r from-orange-500/10 to-amber-500/10 rounded-full blur-xl animate-pulse"></div>
            <div className="absolute bottom-10 right-10 w-32 h-32 bg-gradient-to-r from-amber-500/10 to-orange-600/10 rounded-full blur-2xl animate-pulse"></div>
            <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-gradient-to-r from-orange-400/10 to-amber-500/10 rounded-full blur-lg animate-bounce"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default About;
