@import 'tailwindcss';

/* Custom CSS Variables for Design System */
:root {
  /* Primary Colors - Purple/Violet Theme */
  --color-primary-50: #faf5ff;
  --color-primary-100: #f3e8ff;
  --color-primary-200: #e9d5ff;
  --color-primary-300: #d8b4fe;
  --color-primary-400: #c084fc;
  --color-primary-500: #a855f7;
  --color-primary-600: #9333ea;
  --color-primary-700: #7c3aed;
  --color-primary-800: #6b21a8;
  --color-primary-900: #581c87;

  /* Secondary Colors - Emerald/Green */
  --color-secondary-50: #ecfdf5;
  --color-secondary-100: #d1fae5;
  --color-secondary-200: #a7f3d0;
  --color-secondary-300: #6ee7b7;
  --color-secondary-400: #34d399;
  --color-secondary-500: #10b981;
  --color-secondary-600: #059669;
  --color-secondary-700: #047857;
  --color-secondary-800: #065f46;
  --color-secondary-900: #064e3b;

  /* Accent Colors - Orange/Amber */
  --color-accent-50: #fffbeb;
  --color-accent-100: #fef3c7;
  --color-accent-200: #fde68a;
  --color-accent-300: #fcd34d;
  --color-accent-400: #fbbf24;
  --color-accent-500: #f59e0b;
  --color-accent-600: #d97706;
  --color-accent-700: #b45309;
  --color-accent-800: #92400e;
  --color-accent-900: #78350f;

  /* Neutral Colors */
  --color-neutral-50: #fafafa;
  --color-neutral-100: #f5f5f5;
  --color-neutral-200: #e5e5e5;
  --color-neutral-300: #d4d4d4;
  --color-neutral-400: #a3a3a3;
  --color-neutral-500: #737373;
  --color-neutral-600: #525252;
  --color-neutral-700: #404040;
  --color-neutral-800: #262626;
  --color-neutral-900: #171717;

  /* Background Colors */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #fafafa;
  --color-bg-tertiary: #f5f5f5;
}

/* Typography */
.font-heading {
  font-family: 'Poppins', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.font-body {
  font-family: 'Inter', 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 400;
  line-height: 1.6;
}

/* Global font improvements */
body {
  font-family: 'Inter', 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  font-variant-numeric: oldstyle-nums;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Poppins', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 600;
  letter-spacing: -0.025em;
}

/* Modern font weights */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-neutral-100);
}

::-webkit-scrollbar-thumb {
  background: var(--color-primary-400);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-500);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

/* Gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-700) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--color-secondary-500) 0%, var(--color-secondary-700) 100%);
}

.gradient-accent {
  background: linear-gradient(135deg, var(--color-accent-500) 0%, var(--color-accent-700) 100%);
}

/* Orange/Amber theme gradients */
.gradient-orange-warm {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 50%, #fde68a 100%);
}

.gradient-orange-vibrant {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
}

.gradient-orange-deep {
  background: linear-gradient(135deg, #d97706 0%, #b45309 50%, #92400e 100%);
}

/* Responsive typography improvements */
@media (max-width: 640px) {
  .font-heading {
    line-height: 1.1;
  }

  .font-body {
    line-height: 1.5;
  }

  /* Reduce spacing on mobile */
  .hero-title .block {
    margin-bottom: 0.25rem;
  }

  /* Ensure mobile content doesn't overflow */
  body {
    overflow-x: hidden;
  }

  /* Mobile-specific hero adjustments */
  .hero-title {
    font-size: clamp(1.5rem, 8vw, 2.5rem);
  }

  /* Improve mobile touch targets */
  button {
    min-height: 44px;
    min-width: 44px;
  }

  /* Mobile navigation improvements */
  .mobile-menu {
    max-height: calc(100vh - 4rem);
    overflow-y: auto;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .font-heading {
    line-height: 1.15;
  }
}

/* Ensure proper spacing for hero content */
.hero-content {
  padding-top: 4rem;
}

@media (min-width: 1024px) {
  .hero-content {
    padding-top: 5rem;
  }
}

/* Technology Showcase Animations */
@keyframes scrollLeft {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes scrollRight {
  0% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(0);
  }
}

@keyframes scrollLeftSlow {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-scroll-left {
  animation: scrollLeft 60s linear infinite;
}

.animate-scroll-right {
  animation: scrollRight 60s linear infinite;
}

.animate-scroll-left-slow {
  animation: scrollLeftSlow 80s linear infinite;
}

.tech-scroll-container {
  mask-image: linear-gradient(
    to right,
    transparent 0%,
    black 10%,
    black 90%,
    transparent 100%
  );
  -webkit-mask-image: linear-gradient(
    to right,
    transparent 0%,
    black 10%,
    black 90%,
    transparent 100%
  );
}

.tech-card:hover {
  transform: translateY(-2px);
}
