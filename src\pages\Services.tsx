import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  CpuChipIcon,
  CogIcon,
  EyeIcon,
  CloudIcon,
  CodeBracketIcon,
  LightBulbIcon,
  ArrowRightIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const Services: React.FC = () => {
  const navigate = useNavigate();

  const services = [
    {
      id: 1,
      title: "AI & Machine Learning Development",
      description: "Transform your business with custom AI applications, computer vision systems, and machine learning models that deliver measurable results.",
      icon: <CpuChipIcon className="w-12 h-12" />,
      features: [
        "Custom AI application development",
        "Computer vision solutions",
        "Machine learning model training",
        "Object detection systems",
        "Image processing algorithms",
        "Automated decision systems"
      ],
      technologies: ["TensorFlow", "PyTorch", "OpenCV", "Python", "CUDA", "Docker"]
    },
    {
      id: 2,
      title: "Industrial IoT & Automation",
      description: "Optimize your manufacturing processes with intelligent automation, real-time monitoring, and predictive maintenance solutions.",
      icon: <CogIcon className="w-12 h-12" />,
      features: [
        "Factory automation systems",
        "Smart manufacturing solutions",
        "IoT sensor integration",
        "Real-time monitoring systems",
        "Predictive maintenance",
        "Industrial process optimization"
      ],
      technologies: ["PLC Programming", "SCADA", "IoT Sensors", "MQTT", "Edge Computing", "Industrial Networks"]
    },
    {
      id: 3,
      title: "Computer Vision Applications",
      description: "Implement advanced computer vision systems for quality control, damage detection, and automated visual inspection across industries.",
      icon: <EyeIcon className="w-12 h-12" />,
      features: [
        "Quality control systems",
        "Damage detection solutions",
        "Visual inspection automation",
        "Warehouse management systems",
        "Security and surveillance",
        "Maritime and logistics tracking"
      ],
      technologies: ["OpenCV", "YOLO", "SAM", "Deep Learning", "Image Processing", "Real-time Analytics"]
    },
    {
      id: 4,
      title: "Cloud & Enterprise Solutions",
      description: "Build robust cloud infrastructure with intelligent monitoring, security systems, and enterprise-grade data management solutions.",
      icon: <CloudIcon className="w-12 h-12" />,
      features: [
        "Azure/AWS integration",
        "Cloud infrastructure setup",
        "Security monitoring systems",
        "Log management and analytics",
        "Knowledge graph implementation",
        "Enterprise data management"
      ],
      technologies: ["Azure", "AWS", "Kubernetes", "Microservices", "API Gateway", "DevOps"]
    },
    {
      id: 5,
      title: "Custom Software Development",
      description: "Develop custom applications, APIs, and systems tailored to your specific business requirements with modern technologies and best practices.",
      icon: <CodeBracketIcon className="w-12 h-12" />,
      features: [
        "Web application development",
        "Mobile app development",
        "API development and integration",
        "Database design and optimization",
        "System architecture consulting",
        "Legacy system modernization"
      ],
      technologies: ["React", "Node.js", "Python", "TypeScript", "PostgreSQL", "MongoDB"]
    },
    {
      id: 6,
      title: "Consulting & Strategy",
      description: "Get expert guidance on digital transformation, technology strategy, and implementation roadmaps to maximize your technology investments.",
      icon: <LightBulbIcon className="w-12 h-12" />,
      features: [
        "Digital transformation consulting",
        "AI readiness assessment",
        "Technology stack recommendations",
        "Implementation roadmaps",
        "Training and support",
        "Ongoing maintenance"
      ],
      technologies: ["Strategic Planning", "Technology Assessment", "Process Optimization", "Change Management"]
    }
  ];

  return (
    <div className="pt-20 min-h-screen bg-gradient-to-br from-orange-50 via-white to-amber-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-heading font-bold text-gray-900 mb-6 tracking-tight">
            Our <span className="bg-gradient-to-r from-orange-600 to-amber-700 bg-clip-text text-transparent">Services</span>
          </h1>
          <p className="text-xl text-gray-600 leading-relaxed max-w-4xl mx-auto font-medium">
            Comprehensive software development and technology services designed to transform your business.
            From AI solutions to enterprise systems - we deliver excellence at every step.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {services.map((service) => (
            <div
              key={service.id}
              className="group bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-orange-200 hover:border-orange-400 hover:shadow-xl transition-all duration-300"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-orange-50 to-amber-50 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              <div className="relative z-10">
                {/* Service Header */}
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-orange-500 to-amber-600 flex items-center justify-center text-white shadow-lg group-hover:scale-110 transition-transform duration-300">
                      {service.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-heading font-bold text-gray-900 mb-1">
                        {service.title}
                      </h3>
                    </div>
                  </div>
                </div>

                {/* Description */}
                <p className="text-gray-600 leading-relaxed mb-6">
                  {service.description}
                </p>

                {/* Features */}
                <div className="mb-6">
                  <h4 className="text-sm font-semibold text-gray-900 mb-3">Key Features:</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {service.features.map((feature, index) => (
                      <div key={index} className="flex items-center text-sm text-gray-600">
                        <CheckCircleIcon className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Technologies */}
                <div className="mb-6">
                  <h4 className="text-sm font-semibold text-gray-900 mb-3">Technologies:</h4>
                  <div className="flex flex-wrap gap-2">
                    {service.technologies.map((tech, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-gray-100 text-gray-700 text-xs font-medium rounded-full"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>

                {/* CTA */}
                <button
                  onClick={() => navigate('/portfolio')}
                  className="flex items-center text-orange-600 font-semibold text-sm hover:text-orange-700 transition-colors duration-200 group"
                >
                  View Related Projects
                  <ArrowRightIcon className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-200" />
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center">
          <div className="inline-flex flex-col lg:flex-row items-center space-y-6 lg:space-y-0 lg:space-x-8 p-8 lg:p-12 bg-gradient-to-r from-gray-900 to-gray-800 rounded-3xl text-white shadow-xl">
            <div className="text-center lg:text-left flex-1">
              <h4 className="text-2xl lg:text-3xl font-heading font-bold mb-3">Ready to Transform Your Business?</h4>
              <p className="text-gray-300 text-lg">Let's discuss your project requirements and create a custom solution that drives results.</p>
            </div>
            <div className="w-full lg:w-auto lg:flex-shrink-0 flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
              <button
                onClick={() => navigate('/contact')}
                className="w-full sm:w-auto px-8 py-4 bg-gradient-to-r from-orange-600 to-amber-700 text-white rounded-xl font-semibold text-lg hover:shadow-lg hover:scale-105 transition-all duration-300"
              >
                Schedule Consultation
              </button>
              <button
                onClick={() => navigate('/portfolio')}
                className="w-full sm:w-auto px-8 py-4 bg-transparent border-2 border-white text-white rounded-xl font-semibold text-lg hover:bg-white hover:text-gray-900 transition-all duration-300"
              >
                View Our Work
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Services;
