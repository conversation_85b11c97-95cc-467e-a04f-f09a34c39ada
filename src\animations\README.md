# Animations Directory

## Required Files

### AIRobot.lottie
Please add your `AIRobot.lottie` animation file to this directory for the Hero section (AI slide).

**Expected path:** `src/animations/AIRobot.lottie`

The Hero component uses this animation for the AI-Powered Solutions slide.

### FactoryAutomation.lottie
Please add your `FactoryAutomation.lottie` animation file to this directory for the Hero section (Factory slide).

**Expected path:** `src/animations/FactoryAutomation.lottie`

The Hero component uses this animation for the Factory Automation slide.

### CCTVCamera.lottie
Please add your `CCTVCamera.lottie` animation file to this directory for the Hero section (Image Processing slide).

**Expected path:** `src/animations/CCTVCamera.lottie`

The Hero component uses this animation for the Image Processing slide.

### BusinessTeamDiscusses.lottie
Please add your `BusinessTeamDiscusses.lottie` animation file to this directory for the Development Process section.

**Expected path:** `src/animations/BusinessTeamDiscusses.lottie`

The Development Process component is configured to load this animation file on the left side to showcase your agile methodologies and business approach.

### ContactUs.lottie
Please add your `ContactUs.lottie` animation file to this directory for the Contact section.

**Expected path:** `src/animations/ContactUs.lottie`

The Contact component is configured to load this animation file on the right side to create an engaging contact experience.

### MachineLearning.lottie
Please add your `MachineLearning.lottie` animation file to this directory for the AI Specialization section.

**Expected path:** `src/animations/MachineLearning.lottie`

The AI Specialization component is configured to load this animation file on the left side to showcase your AI and machine learning expertise.

## File Format
- Format: .lottie (DotLottie format)
- Recommended size: Optimized for web (under 1MB each)
- Animation: Should loop seamlessly
- Theme: Should work well with the orange/amber color scheme

## Usage
- **AIRobot.lottie**: Used in Hero section for AI-Powered Solutions slide
- **FactoryAutomation.lottie**: Used in Hero section for Factory Automation slide
- **CCTVCamera.lottie**: Used in Hero section for Image Processing slide
- **MachineLearning.lottie**: Used in AI Specialization section to showcase machine learning and AI expertise
- **BusinessTeamDiscusses.lottie**: Used in Development Process section to illustrate team collaboration and business discussions
- **ContactUs.lottie**: Used in Contact section on the right side to create engaging contact experience
