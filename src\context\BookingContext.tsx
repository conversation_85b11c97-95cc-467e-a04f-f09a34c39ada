import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { BookingState, BookingContextType, BookingFormData, TimeSlot, AvailableSlot } from '../types/booking';
import { bookingService } from '../services/bookingService';

// Initial state
const initialState: BookingState = {
  selectedDate: null,
  selectedTimeSlot: null,
  formData: {},
  availableSlots: [],
  isLoading: false,
  error: null,
  step: 'calendar'
};

// Action types
type BookingAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_SELECTED_DATE'; payload: string | null }
  | { type: 'SET_SELECTED_TIME_SLOT'; payload: TimeSlot | null }
  | { type: 'UPDATE_FORM_DATA'; payload: Partial<BookingFormData> }
  | { type: 'SET_AVAILABLE_SLOTS'; payload: AvailableSlot[] }
  | { type: 'SET_STEP'; payload: BookingState['step'] }
  | { type: 'RESET_BOOKING' };

// Reducer
const bookingReducer = (state: BookingState, action: BookingAction): BookingState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_SELECTED_DATE':
      return { ...state, selectedDate: action.payload };
    case 'SET_SELECTED_TIME_SLOT':
      return { ...state, selectedTimeSlot: action.payload };
    case 'UPDATE_FORM_DATA':
      return { ...state, formData: { ...state.formData, ...action.payload } };
    case 'SET_AVAILABLE_SLOTS':
      return { ...state, availableSlots: action.payload };
    case 'SET_STEP':
      return { ...state, step: action.payload };
    case 'RESET_BOOKING':
      return initialState;
    default:
      return state;
  }
};

// Context
const BookingContext = createContext<BookingContextType | undefined>(undefined);

// Provider component
interface BookingProviderProps {
  children: ReactNode;
}

export const BookingProvider: React.FC<BookingProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(bookingReducer, initialState);

  const actions = {
    setSelectedDate: (date: string) => {
      dispatch({ type: 'SET_SELECTED_DATE', payload: date });
    },

    setSelectedTimeSlot: (slot: TimeSlot) => {
      dispatch({ type: 'SET_SELECTED_TIME_SLOT', payload: slot });
    },

    updateFormData: (data: Partial<BookingFormData>) => {
      dispatch({ type: 'UPDATE_FORM_DATA', payload: data });
    },

    submitBooking: async () => {
      if (!state.selectedDate || !state.selectedTimeSlot || !state.formData.name || !state.formData.email) {
        dispatch({ type: 'SET_ERROR', payload: 'Missing required information' });
        return;
      }

      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      try {
        const bookingData = {
          ...state.formData,
          timeSlotId: state.selectedTimeSlot.id,
          scheduledDate: state.selectedDate,
          scheduledTime: state.selectedTimeSlot.startTime,
          preferredDate: state.selectedDate,
          preferredTime: state.selectedTimeSlot.startTime,
          name: state.formData.name || '',
          email: state.formData.email || '',
          projectType: state.formData.projectType || '',
          message: state.formData.message || ''
        };

        const response = await bookingService.createBooking(bookingData);
        
        if (response.success) {
          dispatch({ type: 'SET_STEP', payload: 'confirmation' });
        } else {
          dispatch({ type: 'SET_ERROR', payload: response.error || 'Booking failed' });
        }
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: 'An unexpected error occurred' });
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    resetBooking: () => {
      dispatch({ type: 'RESET_BOOKING' });
    },

    goToStep: (step: BookingState['step']) => {
      dispatch({ type: 'SET_STEP', payload: step });
    },

    fetchAvailableSlots: async (month: string) => {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      try {
        const response = await bookingService.getAvailableSlots(month);
        
        if (response.success && response.data) {
          dispatch({ type: 'SET_AVAILABLE_SLOTS', payload: response.data });
        } else {
          dispatch({ type: 'SET_ERROR', payload: response.error || 'Failed to load available slots' });
        }
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: 'Failed to load available slots' });
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    }
  };

  const contextValue: BookingContextType = {
    state,
    actions
  };

  return (
    <BookingContext.Provider value={contextValue}>
      {children}
    </BookingContext.Provider>
  );
};

// Hook to use booking context
export const useBooking = (): BookingContextType => {
  const context = useContext(BookingContext);
  if (!context) {
    throw new Error('useBooking must be used within a BookingProvider');
  }
  return context;
};
