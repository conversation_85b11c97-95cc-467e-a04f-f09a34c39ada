import React, { useState } from 'react';
import { DotLottieReact } from '@lottiefiles/dotlottie-react';

interface LottieWithFallbackProps {
  src: string;
  loop?: boolean;
  autoplay?: boolean;
  className?: string;
  fallbackIcon?: React.ReactNode;
  fallbackText?: string;
}

const LottieWithFallback: React.FC<LottieWithFallbackProps> = ({
  src,
  loop = true,
  autoplay = true,
  className = '',
  fallbackIcon,
  fallbackText = 'Animation Loading...'
}) => {
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleError = () => {
    console.warn(`Failed to load Lottie animation: ${src}`);
    setHasError(true);
    setIsLoading(false);
  };

  const handleLoad = () => {
    setIsLoading(false);
  };

  if (hasError) {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <div className="text-center p-8">
          {fallbackIcon ? (
            <div className="w-24 h-24 mx-auto mb-4 text-orange-500">
              {fallbackIcon}
            </div>
          ) : (
            <div className="w-24 h-24 mx-auto mb-4 bg-gradient-to-r from-orange-500 to-amber-600 rounded-full flex items-center justify-center">
              <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
          )}
          <p className="text-gray-500 text-sm">{fallbackText}</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {isLoading && (
        <div className={`flex items-center justify-center ${className}`}>
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
        </div>
      )}
      <DotLottieReact
        src={src}
        loop={loop}
        autoplay={autoplay}
        className={`${className} ${isLoading ? 'hidden' : ''}`}
        onError={handleError}
        onLoad={handleLoad}
      />
    </>
  );
};

export default LottieWithFallback;
