import React, { useState, useEffect } from 'react';
import { DotLottieReact } from '@lottiefiles/dotlottie-react';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
const Hero: React.FC = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const slides = [
    {
      id: 1,
      title: "AI & Machine Learning",
      subtitle: "Next-Gen Software",
      description: "Build intelligent applications with advanced object detection, damage detection apps, and automated agents. From computer vision to cloud automation - we deliver production-ready AI solutions.",
      bgGradient: "from-white via-orange-50 to-amber-100",
      textGradient: "from-orange-600 to-amber-700",
      animation: "/animations/AIRobot.lottie"
    },
    {
      id: 2,
      title: "Industrial IoT Systems",
      subtitle: "Smart Manufacturing",
      description: "Custom software for smart manufacturing, factory automation, and real-time monitoring. Scalable solutions that integrate seamlessly with your existing infrastructure.",
      bgGradient: "from-white via-[#fef9e7] to-[#f4dc84]",
      textGradient: "from-[#c4a852] to-[#a08642]",
      animation: "/animations/Factory.lottie"
    },
    {
      id: 3,
      title: "Computer Vision Apps",
      subtitle: "Visual Intelligence",
      description: "Enterprise-grade image processing solutions for logistics, security, and quality control. From warehouse tracking to maritime operations - powered by cutting-edge algorithms.",
      bgGradient: "from-white via-orange-50 to-amber-100",
      textGradient: "from-orange-600 to-amber-700",
      animation: "/animations/DroneCamera.lottie"
    }
  ];

  // Auto-slide functionality - changes every 30 seconds
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 30000); // Change slide every 30 seconds

    return () => clearInterval(timer);
  }, [slides.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const currentSlideData = slides[currentSlide];

  return (
    <section className={`relative min-h-screen bg-gradient-to-br ${currentSlideData.bgGradient} overflow-hidden pt-16 sm:pt-18 lg:pt-20 transition-all duration-1000`}>
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-20 -right-20 sm:-top-40 sm:-right-40 w-40 h-40 sm:w-80 sm:h-80 bg-orange-300 rounded-full mix-blend-multiply filter blur-xl opacity-60 animate-pulse-slow"></div>
        <div className="absolute -bottom-20 -left-20 sm:-bottom-40 sm:-left-40 w-40 h-40 sm:w-80 sm:h-80 bg-amber-400 rounded-full mix-blend-multiply filter blur-xl opacity-50 animate-pulse-slow"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 sm:w-96 sm:h-96 bg-orange-200 rounded-full mix-blend-multiply filter blur-xl opacity-40 animate-bounce-slow"></div>
        <div className="absolute top-20 left-20 w-32 h-32 sm:w-64 sm:h-64 bg-amber-500 rounded-full mix-blend-multiply filter blur-2xl opacity-30 animate-pulse"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 min-h-[calc(100vh-4rem)] lg:min-h-[calc(100vh-5rem)] flex items-center py-8 sm:py-12 pb-20 sm:pb-24">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center w-full">
          {/* Left side - Content */}
          <div className="text-center lg:text-left animate-fade-in-up order-2 lg:order-1">

            {/* Slide Indicator */}
            <div className="mb-4 sm:mb-6">
              <span className={`inline-block px-3 py-1.5 sm:px-4 sm:py-2 rounded-full text-xs sm:text-sm font-semibold bg-gradient-to-r ${currentSlideData.textGradient} text-white shadow-lg transition-all duration-500`}>
                {currentSlideData.subtitle}
              </span>
            </div>

            <h1 className="hero-title text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-heading font-extrabold text-gray-900 mb-4 sm:mb-6 leading-tight tracking-tight transition-all duration-500">
              <span className="block mb-1 sm:mb-1 text-gray-900">Enterprise</span>
              <span className={`block mb-1 sm:mb-1 bg-gradient-to-r ${currentSlideData.textGradient} bg-clip-text text-transparent`}>
                {currentSlideData.title}
              </span>
              <span className="block text-gray-900">Development</span>
            </h1>

            <p className="text-sm sm:text-base lg:text-lg xl:text-xl text-gray-600 mb-6 sm:mb-8 max-w-lg lg:max-w-xl xl:max-w-2xl mx-auto lg:mx-0 font-body leading-relaxed font-medium transition-all duration-500 px-2 sm:px-0">
              {currentSlideData.description}
            </p>
          </div>

          {/* Right side - Lottie Animation */}
          <div className="flex justify-center lg:justify-end animate-fade-in order-1 lg:order-2 mb-4 lg:mb-0">
            <div className="w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg xl:max-w-xl">
              <DotLottieReact
                src={currentSlideData.animation}
                loop
                autoplay
                className="w-full h-auto transition-all duration-500"
                key={currentSlide} // Force re-render when slide changes
                onError={() => console.warn(`Failed to load ${currentSlideData.animation} animation`)}
              />
            </div>
          </div>
        </div>

        {/* Navigation Arrows - Responsive Positioning */}
        <div className="absolute bottom-4 sm:bottom-6 lg:bottom-8 right-4 sm:right-6 lg:right-8 flex space-x-2 sm:space-x-3 z-20">
          <button
            onClick={prevSlide}
            className="w-10 h-10 sm:w-12 sm:h-12 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-full flex items-center justify-center text-gray-600 hover:text-gray-900 hover:bg-white hover:shadow-lg transition-all duration-300 group"
          >
            <ChevronLeftIcon className="w-4 h-4 sm:w-5 sm:h-5 group-hover:scale-110 transition-transform duration-200" />
          </button>
          <button
            onClick={nextSlide}
            className="w-10 h-10 sm:w-12 sm:h-12 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-full flex items-center justify-center text-gray-600 hover:text-gray-900 hover:bg-white hover:shadow-lg transition-all duration-300 group"
          >
            <ChevronRightIcon className="w-4 h-4 sm:w-5 sm:h-5 group-hover:scale-110 transition-transform duration-200" />
          </button>
        </div>


      </div>
    </section>
  );
};

export default Hero;
