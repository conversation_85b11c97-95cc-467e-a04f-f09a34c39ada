import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ArrowRightIcon,
  CalendarIcon,
  BuildingOfficeIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

const CaseStudies: React.FC = () => {
  const navigate = useNavigate();

  const caseStudies = [
    {
      id: 1,
      client: "Toyota Japan",
      project: "Digital Picking System (DPS)",
      category: "Manufacturing Automation",
      duration: "8 months",
      year: "2023",
      challenge: "Toyota's assembly line faced significant human errors in spare parts picking, leading to production delays and quality issues. The manual process was inefficient and error-prone.",
      solution: "Implemented an advanced RFID-enabled digital picking system with Bluetooth communication, lamp indicators, and audio feedback. The system guides employees to correct parts with real-time validation.",
      results: [
        "60% reduction in human errors",
        "40% increase in productivity",
        "Streamlined assembly process",
        "Real-time inventory tracking",
        "Improved worker satisfaction"
      ],
      technologies: ["RFID Technology", "Bluetooth Communication", "IoT Sensors", "Real-time Processing", "Mobile Integration"],
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=800&h=500&fit=crop",
      clientLogo: "T",
      gradient: "from-blue-500 to-cyan-600"
    },
    {
      id: 2,
      client: "Yokomo Corporation",
      project: "RC Racing Video Analytics",
      category: "Sports Technology",
      duration: "6 months",
      year: "2023",
      challenge: "Yokomo needed advanced analytics for RC car racing to provide real-time insights, performance tracking, and enhanced viewer experience for competitive racing events.",
      solution: "Developed a comprehensive video analysis system using AI and computer vision to track car performance, predict race outcomes, and provide real-time analytics for racers and spectators.",
      results: [
        "Real-time race analytics dashboard",
        "Performance optimization insights",
        "Enhanced viewer engagement",
        "Predictive race modeling",
        "Improved training programs"
      ],
      technologies: ["Video Analytics", "Computer Vision", "AI Prediction Models", "Real-time Processing", "Dashboard Development"],
      image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=500&fit=crop",
      clientLogo: "Y",
      gradient: "from-purple-500 to-pink-600"
    },
    {
      id: 3,
      client: "Global Warehouse Solutions",
      project: "Laser Picking System",
      category: "Warehouse Automation",
      duration: "10 months",
      year: "2022",
      challenge: "Large warehouse operations struggled with accurate product picking, leading to shipping errors, customer complaints, and operational inefficiencies in a complex inventory environment.",
      solution: "Created an AI-powered laser picking system with QR code recognition and computer vision. The system uses laser guidance to direct workers to exact product locations with 99.5% accuracy.",
      results: [
        "99.5% picking accuracy achieved",
        "40% faster picking operations",
        "Reduced shipping errors by 85%",
        "Improved customer satisfaction",
        "ROI achieved in 8 months"
      ],
      technologies: ["Computer Vision", "QR Recognition", "Laser Guidance Systems", "AI Algorithms", "Warehouse Management Integration"],
      image: "https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?w=800&h=500&fit=crop",
      clientLogo: "G",
      gradient: "from-green-500 to-emerald-600"
    },
    {
      id: 4,
      client: "Textile Manufacturing Co.",
      project: "AI Fabric Measurement",
      category: "Quality Control",
      duration: "5 months",
      year: "2023",
      challenge: "Manual fabric measurement processes were time-consuming, inconsistent, and prone to human error, affecting production quality and efficiency in textile manufacturing.",
      solution: "Implemented AI-powered computer vision system for precise fabric measurement and quality control. The system automatically measures dimensions and detects defects in real-time.",
      results: [
        "100% automated measurement process",
        "Precision improved by 95%",
        "50% reduction in processing time",
        "Consistent quality standards",
        "Reduced material waste"
      ],
      technologies: ["Computer Vision", "AI Measurement Algorithms", "Quality Control Systems", "Real-time Processing", "Industrial Integration"],
      image: "https://images.unsplash.com/photo-1558769132-cb1aea458c5e?w=800&h=500&fit=crop",
      clientLogo: "T",
      gradient: "from-indigo-500 to-purple-600"
    },
    {
      id: 5,
      client: "Automotive Manufacturing",
      project: "Tire Damage Detection",
      category: "Quality Assurance",
      duration: "7 months",
      year: "2022",
      challenge: "Car manufacturers needed automated tire quality inspection to ensure safety standards and prevent defective tires from reaching vehicles, replacing slow manual inspection processes.",
      solution: "Developed AI-powered tire damage detection system using advanced image processing to automatically identify defects, cracks, and quality issues before tire installation.",
      results: [
        "100% quality screening coverage",
        "Defect detection accuracy: 98.5%",
        "Enhanced safety standards",
        "Reduced manual inspection time",
        "Zero defective tires in production"
      ],
      technologies: ["AI Image Processing", "Defect Detection Algorithms", "Quality Assurance Systems", "Automated Inspection", "Manufacturing Integration"],
      image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=500&fit=crop",
      clientLogo: "A",
      gradient: "from-red-500 to-orange-600"
    },
    {
      id: 6,
      client: "Electronics Manufacturer",
      project: "Circuit Board Inspection",
      category: "Electronics Quality Control",
      duration: "6 months",
      year: "2023",
      challenge: "Manual inspection of circuit boards and electronic components was slow, inconsistent, and couldn't keep up with high-volume production requirements.",
      solution: "Implemented conveyor-based AI inspection system for continuous circuit board monitoring. Real-time processing ensures all components are correctly positioned and functional.",
      results: [
        "Continuous 24/7 monitoring",
        "Real-time defect detection",
        "99% inspection accuracy",
        "Increased production throughput",
        "Reduced quality control costs"
      ],
      technologies: ["Conveyor Integration", "Real-time AI Processing", "Circuit Analysis", "Quality Control Automation", "Manufacturing Systems"],
      image: "https://images.unsplash.com/photo-1518770660439-4636190af475?w=800&h=500&fit=crop",
      clientLogo: "E",
      gradient: "from-yellow-500 to-orange-600"
    }
  ];

  return (
    <div className="pt-20 min-h-screen bg-gradient-to-br from-orange-50 via-white to-amber-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-orange-600 to-amber-700 text-white rounded-full text-sm font-medium mb-8 shadow-lg">
            <span className="w-3 h-3 bg-white rounded-full mr-3 animate-pulse"></span>
            Client Success Stories
          </div>
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-heading font-bold text-gray-900 mb-6 tracking-tight">
            Proven <span className="bg-gradient-to-r from-orange-600 to-amber-700 bg-clip-text text-transparent">Case Studies</span>
          </h1>
          <p className="text-xl text-gray-600 leading-relaxed max-w-4xl mx-auto font-medium">
            Real clients, real challenges, real results. Discover how we've transformed businesses across industries 
            with innovative technology solutions that deliver measurable impact.
          </p>
        </div>

        {/* Case Studies Grid */}
        <div className="space-y-16">
          {caseStudies.map((study, index) => (
            <div
              key={study.id}
              className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-stretch ${
                index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
              }`}
            >
              {/* Image */}
              <div className={`${index % 2 === 1 ? 'lg:col-start-2' : ''} flex`}>
                <div className="relative rounded-3xl overflow-hidden shadow-xl w-full">
                  <img
                    src={study.image}
                    alt={study.project}
                    className="w-full h-full min-h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  <div className="absolute top-6 left-6">
                    <div className={`w-16 h-16 bg-gradient-to-r ${study.gradient} rounded-2xl flex items-center justify-center text-white font-bold text-2xl shadow-lg`}>
                      {study.clientLogo}
                    </div>
                  </div>
                  <div className="absolute bottom-6 left-6 right-6">
                    <span className="inline-block px-4 py-2 bg-orange-600 text-white text-sm font-semibold rounded-full">
                      {study.category}
                    </span>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className={`${index % 2 === 1 ? 'lg:col-start-1 lg:row-start-1' : ''} flex`}>
                <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-orange-200 shadow-lg w-full h-full flex flex-col">
                  {/* Header */}
                  <div className="mb-6">
                    <div className="flex items-center space-x-4 mb-4">
                      <CalendarIcon className="w-5 h-5 text-orange-600" />
                      <span className="text-sm text-gray-600">{study.year} • {study.duration}</span>
                    </div>
                    <h2 className="text-2xl lg:text-3xl font-heading font-bold text-gray-900 mb-2">
                      {study.client}
                    </h2>
                    <h3 className="text-xl text-orange-600 font-semibold mb-4">
                      {study.project}
                    </h3>
                  </div>

                  {/* Challenge */}
                  <div className="mb-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                      <BuildingOfficeIcon className="w-5 h-5 text-orange-600 mr-2" />
                      Challenge
                    </h4>
                    <p className="text-gray-600 leading-relaxed">
                      {study.challenge}
                    </p>
                  </div>

                  {/* Solution */}
                  <div className="mb-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-3">Solution</h4>
                    <p className="text-gray-600 leading-relaxed">
                      {study.solution}
                    </p>
                  </div>

                  {/* Results */}
                  <div className="mb-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                      <ChartBarIcon className="w-5 h-5 text-green-600 mr-2" />
                      Results
                    </h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      {study.results.map((result, resultIndex) => (
                        <div key={resultIndex} className="flex items-center text-sm text-gray-600">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-3 flex-shrink-0"></div>
                          {result}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Technologies */}
                  <div>
                    <h4 className="text-sm font-semibold text-gray-900 mb-3">Technologies Used:</h4>
                    <div className="flex flex-wrap gap-2">
                      {study.technologies.map((tech, techIndex) => (
                        <span 
                          key={techIndex}
                          className="px-3 py-1 bg-orange-100 text-orange-700 text-xs font-medium rounded-full"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-20">
          <div className="inline-flex flex-col lg:flex-row items-center space-y-6 lg:space-y-0 lg:space-x-8 p-8 lg:p-12 bg-gradient-to-r from-gray-900 to-gray-800 rounded-3xl text-white shadow-xl">
            <div className="text-center lg:text-left flex-1">
              <h4 className="text-2xl lg:text-3xl font-heading font-bold mb-3">Ready to Create Your Success Story?</h4>
              <p className="text-gray-300 text-lg">Let's discuss how we can solve your unique challenges and deliver measurable results.</p>
            </div>
            <div className="w-full lg:w-auto lg:flex-shrink-0 flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
              <button 
                onClick={() => navigate('/booking')}
                className="w-full sm:w-auto px-8 py-4 bg-gradient-to-r from-orange-600 to-amber-700 text-white rounded-xl font-semibold text-lg hover:shadow-lg hover:scale-105 transition-all duration-300"
              >
                Start Your Project
              </button>
              <button 
                onClick={() => navigate('/services')}
                className="w-full sm:w-auto px-8 py-4 bg-transparent border-2 border-white text-white rounded-xl font-semibold text-lg hover:bg-white hover:text-gray-900 transition-all duration-300"
              >
                View Our Services
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CaseStudies;
