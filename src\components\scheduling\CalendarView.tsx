import React, { useState, useEffect } from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import {
  formatDate,
  getCalendarDays,
  getMonthYear,
  isToday,
  isPastDate
} from '../../utils/dateUtils';
import LoadingSpinner from '../ui/LoadingSpinner';
import { bookingService } from '../../services/bookingService';

interface CalendarViewProps {
  onDateSelect: (date: string) => void;
  selectedDate: string | null;
}

const CalendarView: React.FC<CalendarViewProps> = ({ onDateSelect, selectedDate }) => {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [availableDates, setAvailableDates] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadAvailableSlots();
  }, [currentMonth]);

  const loadAvailableSlots = async () => {
    setIsLoading(true);
    try {
      const monthStr = formatDate(currentMonth).substring(0, 7); // YYYY-MM
      console.log('Loading slots for month:', monthStr);
      const response = await bookingService.getAvailableSlots(monthStr);

      console.log('Booking service response:', response);

      if (response.success && response.data) {
        const dates = new Set(response.data.map(slot => slot.date));
        console.log('Available dates loaded:', Array.from(dates));
        setAvailableDates(dates);
      } else {
        console.log('No available slots found');
        setAvailableDates(new Set());
      }
    } catch (error) {
      console.error('Failed to load available slots:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const calendarDays = getCalendarDays(currentMonth);
  const today = new Date();

  const goToPreviousMonth = () => {
    setCurrentMonth(prev => new Date(prev.getFullYear(), prev.getMonth() - 1, 1));
  };

  const goToNextMonth = () => {
    setCurrentMonth(prev => new Date(prev.getFullYear(), prev.getMonth() + 1, 1));
  };

  const handleDateClick = (date: Date) => {
    const dateStr = formatDate(date);

    console.log('Date clicked:', dateStr);
    console.log('Is past date:', isPastDate(date));
    console.log('Is available:', availableDates.has(dateStr));
    console.log('Available dates:', Array.from(availableDates));

    // Don't allow selection of past dates or unavailable dates
    if (isPastDate(date) || !availableDates.has(dateStr)) {
      console.log('Date selection blocked');
      return;
    }

    console.log('Selecting date:', dateStr);
    onDateSelect(dateStr);
  };

  const getDayClasses = (date: Date) => {
    const dateStr = formatDate(date);
    const isCurrentMonth = date.getMonth() === currentMonth.getMonth();
    const isPast = isPastDate(date);
    const isAvailable = availableDates.has(dateStr);
    const isSelected = selectedDate === dateStr;
    const isTodayDate = isToday(date);

    let classes = 'w-10 h-10 flex items-center justify-center text-sm font-medium rounded-xl transition-all duration-200 cursor-pointer ';

    if (!isCurrentMonth) {
      classes += 'text-gray-300 ';
    } else if (isPast || !isAvailable) {
      classes += 'text-gray-400 cursor-not-allowed ';
    } else if (isSelected) {
      classes += 'bg-orange-500 text-white shadow-lg ';
    } else if (isTodayDate) {
      classes += 'bg-orange-100 text-orange-700 border-2 border-orange-300 ';
    } else {
      classes += 'text-gray-700 hover:bg-orange-50 hover:text-orange-600 ';
    }

    if (isAvailable && !isPast && isCurrentMonth) {
      classes += 'hover:scale-105 ';
    }

    return classes;
  };

  const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <div className="p-4 sm:p-6 lg:p-8">
      <div className="max-w-lg mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={goToPreviousMonth}
            className="p-2 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-xl transition-colors duration-200"
          >
            <ChevronLeftIcon className="w-5 h-5" />
          </button>
          
          <h2 className="text-xl font-heading font-bold text-gray-900">
            {getMonthYear(currentMonth)}
          </h2>
          
          <button
            onClick={goToNextMonth}
            className="p-2 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-xl transition-colors duration-200"
          >
            <ChevronRightIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex justify-center py-8">
            <LoadingSpinner size="lg" />
          </div>
        )}

        {/* Calendar Grid */}
        {!isLoading && (
          <div className="bg-gray-50 rounded-2xl border border-gray-200 p-3 sm:p-4">
            {/* Week Days Header */}
            <div className="grid grid-cols-7 gap-1 mb-2">
              {weekDays.map(day => (
                <div key={day} className="text-center text-xs font-semibold text-gray-500 py-2">
                  {day}
                </div>
              ))}
            </div>

            {/* Calendar Days */}
            <div className="grid grid-cols-7 gap-1">
              {calendarDays.map((date, index) => (
                <button
                  key={index}
                  onClick={() => handleDateClick(date)}
                  className={getDayClasses(date)}
                  disabled={isPastDate(date) || !availableDates.has(formatDate(date))}
                >
                  {date.getDate()}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Legend */}
        <div className="mt-6 flex items-center justify-center space-x-6 text-xs text-gray-600">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
            <span>Selected</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-orange-100 border-2 border-orange-300 rounded-full"></div>
            <span>Today</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-gray-100 rounded-full"></div>
            <span>Available</span>
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-4 text-center">
          <p className="text-sm text-gray-600">
            Select a date to view available time slots
          </p>
        </div>
      </div>
    </div>
  );
};

export default CalendarView;
