import React from 'react';
import { 
  CheckCircleIcon, 
  CalendarIcon, 
  ClockIcon, 
  EnvelopeIcon,
  PhoneIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import { formatDisplayDate, formatDisplayTime } from '../../utils/dateUtils';
import { Booking, TimeSlot } from '../../types/booking';
import Button from '../ui/Button';

interface BookingConfirmationProps {
  bookingData: Booking;
  selectedDate: string;
  selectedTimeSlot: TimeSlot;
  onClose: () => void;
}

const BookingConfirmation: React.FC<BookingConfirmationProps> = ({
  bookingData,
  selectedDate,
  selectedTimeSlot,
  onClose
}) => {
  const date = new Date(selectedDate);

  const handleAddToCalendar = () => {
    // Create calendar event
    const startDate = new Date(`${selectedDate}T${selectedTimeSlot.startTime}:00`);
    const endDate = new Date(`${selectedDate}T${selectedTimeSlot.endTime}:00`);
    
    const event = {
      title: 'Consultation with Mothercode Software',
      start: startDate.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z',
      end: endDate.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z',
      description: `Project consultation for ${bookingData.projectType}. ${bookingData.message}`,
      location: 'Online Meeting'
    };

    // Google Calendar URL
    const googleCalendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(event.title)}&dates=${event.start}/${event.end}&details=${encodeURIComponent(event.description)}&location=${encodeURIComponent(event.location)}`;
    
    window.open(googleCalendarUrl, '_blank');
  };

  const handleSendEmail = () => {
    const subject = `Consultation Booking Confirmation - ${formatDisplayDate(date)}`;
    const body = `Hi,

Thank you for booking a consultation with Mothercode Software System.

Booking Details:
- Date: ${formatDisplayDate(date)}
- Time: ${formatDisplayTime(selectedTimeSlot.startTime)} - ${formatDisplayTime(selectedTimeSlot.endTime)}
- Project Type: ${bookingData.projectType}
- Booking ID: ${bookingData.id}

We'll send you a meeting link closer to the appointment time.

Best regards,
Mothercode Software Team`;

    window.location.href = `mailto:${bookingData.email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
  };

  return (
    <div className="p-4 sm:p-6 lg:p-8">
      <div className="max-w-2xl mx-auto text-center">
        {/* Success Icon */}
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <CheckCircleIcon className="w-8 h-8 text-green-600" />
        </div>

        {/* Success Message */}
        <h2 className="text-2xl font-heading font-bold text-gray-900 mb-2">
          Booking Confirmed!
        </h2>
        <p className="text-gray-600 mb-8">
          Your consultation has been successfully scheduled. We'll send you a confirmation email shortly.
        </p>

        {/* Booking Details Card */}
        <div className="bg-gradient-to-br from-orange-50 to-amber-50 rounded-2xl p-6 mb-8 text-left">
          <h3 className="font-heading font-bold text-gray-900 mb-4 text-center">
            Booking Details
          </h3>
          
          <div className="space-y-4">
            {/* Date & Time */}
            <div className="flex items-start space-x-3">
              <CalendarIcon className="w-5 h-5 text-orange-600 mt-0.5" />
              <div>
                <p className="font-semibold text-gray-900">
                  {formatDisplayDate(date)}
                </p>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <ClockIcon className="w-4 h-4" />
                  <span>
                    {formatDisplayTime(selectedTimeSlot.startTime)} - {formatDisplayTime(selectedTimeSlot.endTime)}
                  </span>
                </div>
              </div>
            </div>

            {/* Contact Info */}
            <div className="flex items-start space-x-3">
              <UserIcon className="w-5 h-5 text-orange-600 mt-0.5" />
              <div>
                <p className="font-semibold text-gray-900">{bookingData.name}</p>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <EnvelopeIcon className="w-4 h-4" />
                  <span>{bookingData.email}</span>
                </div>
                {bookingData.phone && (
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <PhoneIcon className="w-4 h-4" />
                    <span>{bookingData.phone}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Project Type */}
            <div className="pt-3 border-t border-orange-200">
              <p className="text-sm text-gray-600">Project Type</p>
              <p className="font-semibold text-gray-900 capitalize">
                {bookingData.projectType.replace('-', ' ')}
              </p>
            </div>

            {/* Booking ID */}
            <div className="pt-3 border-t border-orange-200">
              <p className="text-sm text-gray-600">Booking ID</p>
              <p className="font-mono text-sm text-gray-900">{bookingData.id}</p>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="bg-blue-50 rounded-2xl p-6 mb-8 text-left">
          <h4 className="font-heading font-bold text-gray-900 mb-3">What's Next?</h4>
          <ul className="space-y-2 text-sm text-gray-700">
            <li className="flex items-start space-x-2">
              <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
              <span>You'll receive a confirmation email with meeting details</span>
            </li>
            <li className="flex items-start space-x-2">
              <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
              <span>We'll send you a meeting link 24 hours before the consultation</span>
            </li>
            <li className="flex items-start space-x-2">
              <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
              <span>Prepare any questions or materials you'd like to discuss</span>
            </li>
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <Button
            onClick={handleAddToCalendar}
            variant="primary"
            className="w-full flex items-center justify-center space-x-2"
          >
            <CalendarIcon className="w-5 h-5" />
            <span>Add to Calendar</span>
          </Button>
          
          <Button
            onClick={handleSendEmail}
            variant="outline"
            className="w-full flex items-center justify-center space-x-2"
          >
            <EnvelopeIcon className="w-5 h-5" />
            <span>Email Details</span>
          </Button>
          
          <Button
            onClick={onClose}
            variant="ghost"
            className="w-full"
          >
            Close
          </Button>
        </div>

        {/* Contact Info */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <p className="text-sm text-gray-600">
            Need to reschedule or have questions?
          </p>
          <p className="text-sm text-orange-600 font-medium">
            Contact <NAME_EMAIL>
          </p>
        </div>
      </div>
    </div>
  );
};

export default BookingConfirmation;
